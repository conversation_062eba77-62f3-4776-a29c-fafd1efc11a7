import shutil
import tkinter as tk
from tkinter import ttk, simpledialog, messagebox, filedialog
from PIL import Image, ImageTk
import datetime
import json
import os
import webbrowser
from tkinter import font as tkfont

# إعدادات النظام
SYSTEM_NAME = "نظام نور البيان لإدارة مراكز تحفيظ القرآن الكريم"
VERSION = "3.0"  # تم تحديث الإصدار
DEVELOPER = "فريق نور البيان"
CONTACT_EMAIL = "<EMAIL>"
WEBSITE = "www.noor-albyan.com"

# ألوان جديدة للواجهة
COLORS = {
    "primary": "#0C356A",
    "secondary": "#279EFF",
    "accent": "#40F8FF",
    "light": "#D5FFD0",
    "dark": "#1B1A17",
    "success": "#28a745",
    "warning": "#ffc107",
    "danger": "#dc3545",
    "info": "#17a2b8"
}

# خطوط عربية جميلة
FONTS = {
    "title": ("Cairo", 24, "bold"),
    "subtitle": ("Cairo", 16, "bold"),
    "normal": ("Cairo", 14),
    "small": ("Cairo", 12),
    "large": ("Cairo", 18)
}

# بيانات النظام
DATA_FILES = {
    "users": "users.json",
    "centers": "centers.json",
    "teachers": "teachers.json"
}

# تحميل البيانات من ملفات JSON
def load_data():
    global USERS, CENTERS, TEACHERS
    try:
        with open(DATA_FILES["users"], "r", encoding="utf-8") as f:
            USERS = json.load(f)
    except:
        USERS = [
            {
                 "center_id": "CEN001",
                 "name": "مركز الهداية",
                 "email": "<EMAIL>",
                 "address": "شارع القرآن الكريم، الرياض",
                 "phone": "0112345678",
                 "manager": "center1",
                 "manager_details": {
                      "full_name": "علي محمد",
                      "birth_date": "1985-05-15",
                      "personal_phone": "0512345678",
                      "marital_status": "متزوج"
                 },
                 "teachers": ["teacher1"],
                 "created_date": "2025-01-01"

            },
            {
                "user_id": "MGR001",
                "username": "center1",
                "password": "1111",
                "role": "إدارة المركز",
                "full_name": "علي محمد",
                "birth_date": "1985-05-15",
                "phone": "0512345678",
                "address": "الرياض",
                "marital_status": "متزوج",
                "center": "مركز الهداية",
                "last_login": None
            },
            {
                "user_id": "TEA001",
                "username": "teacher1",
                "password": "2222",
                "role": "هيئة التدريس",
                "full_name": "أحمد عبد الرحمن",
                "birth_date": "1990-08-20",
                "phone": "0511111111",
                "address": "حي النخيل، الرياض",
                "marital_status": "أعزب",
                "center": "مركز الهداية",
                "last_login": None
            }
        ]

    try:
        with open(DATA_FILES["centers"], "r", encoding="utf-8") as f:
            CENTERS = json.load(f)
    except:
        CENTERS = [
            {
                "center_id": "CEN001",
                "name": "مركز الهداية",
                "email": "<EMAIL>",
                "address": "شارع القرآن الكريم، الرياض",
                "phone": "0112345678",
                "manager": "center1",
                "manager_details": {
                    "full_name": "علي محمد",
                    "birth_date": "1985-05-15",
                    "personal_phone": "0512345678",
                    "marital_status": "متزوج"
                },
                "teachers": ["teacher1"],
                "created_date": "2025-01-01"
            }
        ]

    try:
        with open(DATA_FILES["teachers"], "r", encoding="utf-8") as f:
            TEACHERS = json.load(f)
    except:
        TEACHERS = [
            {
                "teacher_id": "TEA001",
                "username": "teacher1",
                "full_name": "أحمد عبد الرحمن",
                "birth_date": "1990-08-20",
                "phone": "0511111111",
                "address": "حي النخيل، الرياض",
                "marital_status": "أعزب",
                "center": "مركز الهداية",
                "students": [
                    {
                        "student_id": "STU001",
                        "name": "محمد عبد الله",
                        "birth_date": "2015-03-10",
                        "level": "متوسط",
                        "current_surah": "البقرة",
                        "performance": "جيد جدًا",
                        "parent_name": "عبد الله محمد",
                        "parent_contact": "0600000000",
                        "parent_address": "حي العليا، الرياض",
                        "join_date": "2025-01-15",
                        "plan": "3 سنوات",
                        "period": "صباحية",
                        "recitations": [
                            {
                                "type": "روتيني",
                                "date": "2025-07-04",
                                "time": "صباحية",
                                "surah": "البقرة",
                                "ayahs": "1-5",
                                "score": "ممتاز",
                                "mistakes": "لا يوجد"
                            }
                        ],
                        "attendance": []
                    }
                ]
            }
        ]

# حفظ البيانات إلى ملفات JSON
def save_data():
    try:
        with open(DATA_FILES["users"], "w", encoding="utf-8") as f:
            json.dump(USERS, f, ensure_ascii=False, indent=2)
        with open(DATA_FILES["centers"], "w", encoding="utf-8") as f:
            json.dump(CENTERS, f, ensure_ascii=False, indent=2)
        with open(DATA_FILES["teachers"], "w", encoding="utf-8") as f:
            json.dump(TEACHERS, f, ensure_ascii=False, indent=2)
    except Exception as e:
        messagebox.showerror("خطأ في الحفظ", f"حدث خطأ أثناء حفظ البيانات: {str(e)}")

# تحميل البيانات عند بدء التشغيل
load_data()

# وظائف مساعدة
def get_center_by_manager(username):
     for center in CENTERS:
          if center["manager"] == username:
               return center
     return None

def get_teacher_data(username):
     for t in TEACHERS:
          if t["username"] == username:
               return t
     return None

def get_teachers_in_center(center_name):
     return [t for t in TEACHERS if t["center"] == center_name]

def get_all_students_in_center(center_name):
     students = []
     for t in TEACHERS:
          if t["center"] == center_name:
               for s in t["students"]:
                    students.append({"teacher": t["username"], **s})
     return students

def get_all_students():
     students = []
     for t in TEACHERS:
          for s in t["students"]:
               students.append({"teacher": t["username"], "center": t["center"], **s})
     return students

def get_today_date():
     return datetime.datetime.now().strftime("%Y-%m-%d")

def get_current_time():
     return datetime.datetime.now().strftime("%H:%M")

def get_week_number():
     return datetime.datetime.now().strftime("%U")

def center_window(window, width, height):
     screen_width = window.winfo_screenwidth()
     screen_height = window.winfo_screenheight()
     x = (screen_width - width) // 2
     y = (screen_height - height) // 3
     window.geometry(f"{width}x{height}+{x}+{y}")
# ----- ملف الطالب مع تحسينات -----
def open_student_file(student, parent_tab=None):
     teacher_username = student.get("teacher", "")
     teacher_data = get_teacher_data(teacher_username) if teacher_username else None
     win = tk.Toplevel()
     win.title(f"ملف الطالب {student['name']}")
     win.geometry("1000x800")
     center_window(win, 1000, 800)

     # إطار المعلومات الأساسية
     info_frame = tk.Frame(win)
     info_frame.pack(fill="x", padx=10, pady=10)

     # بيانات الطالب
     tk.Label(info_frame, text=f"رقم الطالب: {student.get('student_id', '')}", font=FONTS["normal"]).grid(row=0, column=1,
                                                                                                          sticky="e")
     tk.Label(info_frame, text=f"الاسم: {student['name']}", font=FONTS["normal"]).grid(row=1, column=1, sticky="e")
     tk.Label(info_frame, text=f"تاريخ الميلاد: {student.get('birth_date', '')}", font=FONTS["normal"]).grid(row=2, column=1,
                                                                                                             sticky="e")
     tk.Label(info_frame, text=f"المستوى: {student['level']}", font=FONTS["normal"]).grid(row=3, column=1, sticky="e")
     tk.Label(info_frame, text=f"خطة الحفظ: {student.get('plan', '')}", font=FONTS["normal"]).grid(row=4, column=1,
                                                                                                   sticky="e")
     tk.Label(info_frame, text=f"الفترة: {student.get('period', '')}", font=FONTS["normal"]).grid(row=5, column=1,
                                                                                                  sticky="e")

     # بيانات ولي الأمر
     tk.Label(info_frame, text=f"اسم ولي الأمر: {student.get('parent_name', '')}", font=FONTS["normal"]).grid(row=0,
                                                                                                              column=0,
                                                                                                              sticky="e")
     tk.Label(info_frame, text=f"هاتف ولي الأمر: {student.get('parent_contact', '')}", font=FONTS["normal"]).grid(row=1,
                                                                                                                  column=0,
                                                                                                                  sticky="e")
     tk.Label(info_frame, text=f"عنوان ولي الأمر: {student.get('parent_address', '')}", font=FONTS["normal"]).grid(row=2,
                                                                                                                   column=0,
                                                                                                                   sticky="e")

     # إطار التبويبات
     notebook = ttk.Notebook(win)
     notebook.pack(fill="both", expand=True, padx=10, pady=10)

     # تبويب التسميعات
     recitation_frame = tk.Frame(notebook)
     notebook.add(recitation_frame, text="سجل التسميع")

     cols = ("النوع", "التاريخ", "الفترة", "السورة", "من-إلى", "التقييم", "الأخطاء")
     tree = ttk.Treeview(recitation_frame, columns=cols, show="headings", style="Custom.Treeview")

     for c in cols:
          tree.heading(c, text=c)
          tree.column(c, anchor="center", width=100)

     scroll = ttk.Scrollbar(recitation_frame, orient="vertical", command=tree.yview)
     tree.configure(yscrollcommand=scroll.set)
     scroll.pack(side="left", fill="y")
     tree.pack(side="right", fill="both", expand=True)

     for rec in student["recitations"]:
          tree.insert("", "end", values=(
               rec["type"],
               rec["date"],
               rec.get("time", ""),
               rec["surah"],
               rec["ayahs"],
               rec["score"],
               rec["mistakes"]
          ))


     # تبويب الحضور
     attendance_frame = tk.Frame(notebook)
     notebook.add(attendance_frame, text="سجل الحضور")
     tk.Label(attendance_frame, text="سجل الحضور سيظهر هنا", font=FONTS["normal"]).pack(pady=50)

     # تبويب الملاحظات
     notes_frame = tk.Frame(notebook)
     notebook.add(notes_frame, text="ملاحظات المعلم")
     notes_text = tk.Text(notes_frame, font=FONTS["small"], height=10, wrap="word")
     notes_text.pack(fill="both", expand=True, padx=5, pady=5)
     notes_text.insert("1.0", student.get("notes", "لا توجد ملاحظات"))

     def save_notes():
          student["notes"] = notes_text.get("1.0", "end-1c")
          messagebox.showinfo("تم", "تم حفظ الملاحظات بنجاح")

     tk.Button(notes_frame, text="حفظ الملاحظات", font=FONTS["small"], bg=COLORS["success"], fg="white",
               command=save_notes).pack(pady=5)

     # وظائف التحكم
     control_frame = tk.Frame(win)
     control_frame.pack(fill="x", pady=10)

     # إضافة زر التسميع فقط إذا كان المستخدم معلم
     if teacher_data and teacher_data.get("role") == "هيئة التدريس":
          tk.Button(control_frame, text="إضافة تسميع", font=FONTS["normal"],
                    bg=COLORS["secondary"], fg="white", command=lambda: add_recitation(student)).pack(side="right", padx=5)

# إصلاح مشكلة دالة add_recitation
def add_recitation(student, parent_window=None):
     """إضافة تسميع جديد للطالب مع تحسينات في الواجهة والتحقق من البيانات"""
     current_user = None
     if 'CURRENT_USER' in globals():
          current_user = globals()['CURRENT_USER']
     username = current_user.get('username', 'system') if current_user else 'system'
     dialog = tk.Toplevel(parent_window if parent_window else None)
     dialog.title("إضافة تسميع جديد")
     dialog.geometry("650x650")  # زيادة حجم النافذة قليلاً
     center_window(dialog, 650, 650)

     # إطار العنوان مع تحسينات التصميم
     header_frame = tk.Frame(dialog, bg=COLORS["primary"])
     header_frame.pack(fill="x", pady=(0, 10))

     tk.Label(header_frame, text="إضافة تسميع جديد",
              font=FONTS["subtitle"], bg=COLORS["primary"], fg="white").pack(pady=10)

     # إطار التمرير للنموذج
     main_frame = tk.Frame(dialog)
     main_frame.pack(fill="both", expand=True)

     canvas = tk.Canvas(main_frame)
     scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
     scrollable_frame = tk.Frame(canvas)

     scrollable_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
     canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
     canvas.configure(yscrollcommand=scrollbar.set)

     scrollbar.pack(side="left", fill="y")
     canvas.pack(side="right", fill="both", expand=True)

     fields = [
          {"label": "نوع التسميع", "type": "combobox", "values": ["روتيني", "دوري"], "required": True},
          {"label": "التاريخ", "type": "entry", "default": get_today_date(), "required": True},
          {"label": "الفترة", "type": "combobox", "values": ["صباحية", "مسائية"], "required": True},
          {"label": "السورة", "type": "entry", "default": student.get("current_surah", ""), "required": True},
          {"label": "من آية", "type": "entry", "default": "", "required": True},
          {"label": "إلى آية", "type": "entry", "default": "", "required": True},
          {"label": "التقييم", "type": "combobox",
           "values": ["ممتاز", "جيد جدًا", "جيد", "مقبول", "ضعيف"], "required": True},
          {"label": "الأخطاء", "type": "entry", "default": "", "required": False},
          {"label": "ملاحظات إضافية", "type": "text", "default": "", "required": False}
     ]

     entries = []
     for field in fields:
          frame = tk.Frame(scrollable_frame, padx=10, pady=5)
          frame.pack(fill="x")

          tk.Label(frame, text=f"{field['label']}:", font=FONTS["small"],
                   width=15, anchor="e").pack(side="right", padx=5)

          if field["type"] == "combobox":
               var = tk.StringVar()
               combo = ttk.Combobox(frame, textvariable=var,
                                    values=field["values"],
                                    font=FONTS["small"], state="readonly")
               if "default" in field:
                    var.set(field["default"])
               combo.pack(fill="x", expand=True)
               entries.append(var)
          elif field["type"] == "text":
               text = tk.Text(frame, height=4, font=FONTS["small"], wrap="word")
               if "default" in field:
                    text.insert("1.0", field["default"])
               text.pack(fill="x", expand=True)
               entries.append(text)
          else:
               entry = ttk.Entry(frame, font=FONTS["small"])
               if "default" in field:
                    entry.insert(0, field["default"])
               entry.pack(fill="x", expand=True)
               entries.append(entry)

     def save_recitation():
          # التحقق من الحقول المطلوبة
          for i, field in enumerate(fields):
               if field["required"] and not entries[i].get():
                    messagebox.showerror("خطأ", f"حقل '{field['label']}' مطلوب")
                    return

          recitation_type = entries[0].get()

          # إذا كان التسميع دوري، التحقق من أن الطالب مؤهل
          if recitation_type == "دوري" and not is_qualified_for_periodic(student):
               messagebox.showerror("خطأ", "هذا الطالب لم يكمل 10 أجزاء بعد، لا يمكن تسجيل تسميع دوري")
               return

          # التحقق من صحة نطاق الآيات
          try:
               from_ayah = int(entries[4].get())
               to_ayah = int(entries[5].get())
               if from_ayah > to_ayah:
                    messagebox.showerror("خطأ", "رقم الآية الأولى يجب أن يكون أصغر أو يساوي الآية الأخيرة")
                    return
          except ValueError:
               messagebox.showerror("خطأ", "يجب إدخال أرقام صحيحة لآيات البداية والنهاية")
               return

          new_recitation = {
               "type": recitation_type,
               "date": entries[1].get(),
               "time": entries[2].get(),
               "surah": entries[3].get(),
               "ayahs": f"{entries[4].get()}-{entries[5].get()}",
               "score": entries[6].get(),
               "mistakes": entries[7].get(),
               "notes": entries[8].get("1.0", "end-1c") if len(entries) > 8 else "",
               "added_by": username,  # استخدام المتغير الذي عرفناه بأمان
               "added_at": f"{get_today_date()} {get_current_time()}"
          }

          student["recitations"].append(new_recitation)

          # تحديث السورة الحالية إذا كانت مختلفة
          if entries[3].get() != student.get("current_surah", ""):
               student["current_surah"] = entries[3].get()

          save_data()  # حفظ التغييرات
          dialog.destroy()
          messagebox.showinfo("تم", "تم إضافة التسميع بنجاح")

          # تحديث الواجهة إذا كانت متاحة
          if parent_window and hasattr(parent_window, 'refresh'):
               parent_window.refresh()

     # إطار الأزرار
     button_frame = tk.Frame(dialog)
     button_frame.pack(fill="x", pady=10)

     tk.Button(button_frame, text="حفظ", font=FONTS["normal"],
               bg=COLORS["success"], fg="white",
               command=save_recitation).pack(side="right", padx=10, ipadx=20)

     tk.Button(button_frame, text="إلغاء", font=FONTS["normal"],
               bg=COLORS["danger"], fg="white",
               command=dialog.destroy).pack(side="left", padx=10, ipadx=20)

# إصلاح دالة التأهل للتسميع الدوري
def is_qualified_for_periodic(student):
     """
     تحقق إذا كان الطالب مؤهلاً للتسميع الدوري

     المعايير:
     1. إكمال 10 أجزاء من القرآن (حسب نظام المركز)
     2. أن يكون الطالب في مستوى متقدم (متوسط أو أعلى)
     3. أن يكون قد مضى على انضمامه للمركز فترة كافية

     Args:
         student (dict): بيانات الطالب

     Returns:
         bool: True إذا كان مؤهلاً، False إذا لم يكن مؤهلاً
     """
     # 1. التحقق من عدد الأجزاء المكتملة
     # نفترض أن كل 5 تسميعات كاملة تمثل جزءاً (يمكن تعديل الرقم حسب نظام المركز)
     required_completed_parts = 10  # عدد الأجزاء المطلوبة
     recitations_per_part = 5  # عدد التسميعات لكل جزء

     # حساب الأجزاء المكتملة بناء على التسميعات الكاملة
     completed_parts = len([r for r in student.get("recitations", [])
                            if r.get("score") in ["ممتاز", "جيد جدًا"]]) // recitations_per_part

     # 2. التحقق من مستوى الطالب
     student_level = student.get("level", "مبتدئ")
     advanced_levels = ["متوسط", "متقدم", "متميز"]

     # 3. التحقق من مدة الانضمام (3 أشهر على الأقل)
     join_date = student.get("join_date", "")
     if join_date and "-" in join_date:
          try:
               join_date = datetime.datetime.strptime(join_date, "%Y-%m-%d")
          except ValueError:
               join_date = None
     else:
          join_date = None
          months_joined = (datetime.datetime.now() - join_date).days // 30

     # تطبيق شروط التأهل:
     # - إكمال 10 أجزاء على الأقل
     # - أن يكون في مستوى متوسط أو أعلى
     # - أن يكون مضى على انضمامه 3 أشهر على الأقل
     return (completed_parts >= required_completed_parts and
             student_level in advanced_levels and
             (join_date and months_joined >= 3))

def print_report(student=None, parent_window=None):
     """
     إنشاء وتصدير تقرير مفصل عن الطالب بصيغة PDF

     Args:
         student (dict): بيانات الطالب (اختياري)
         parent_window (tk.Window): النافذة الأم (اختياري)
     """
     if not student:
          messagebox.showerror("خطأ", "لم يتم تحديد طالب لإنشاء التقرير")
          return

     try:
          # إنشاء النافذة الخاصة بخيارات التقرير
          report_dialog = tk.Toplevel(parent_window)
          report_dialog.title("خيارات التقرير")
          report_dialog.geometry("500x400")
          center_window(report_dialog, 500, 400)

          # إطار خيارات التقرير
          options_frame = tk.Frame(report_dialog, padx=20, pady=20)
          options_frame.pack(fill="both", expand=True)

          # عناصر واجهة خيارات التقرير
          tk.Label(options_frame, text="إعدادات التقرير",
                   font=FONTS["subtitle"], fg=COLORS["primary"]).pack(pady=10)

          # نوع التقرير
          report_type = tk.StringVar(value="كامل")
          tk.Label(options_frame, text="نوع التقرير:").pack(anchor="w")
          ttk.Combobox(options_frame, textvariable=report_type,
                       values=["كامل", "مختصر", "التسميعات فقط", "الحضور فقط"],
                       state="readonly").pack(fill="x", pady=5)

          # إدراج صورة الطالب
          include_photo = tk.BooleanVar(value=True)
          tk.Checkbutton(options_frame, text="إدراج صورة الطالب",
                         variable=include_photo).pack(anchor="w", pady=5)

          # تنسيق التصدير
          export_format = tk.StringVar(value="PDF")
          tk.Label(options_frame, text="صيغة الملف:").pack(anchor="w")
          ttk.Combobox(options_frame, textvariable=export_format,
                       values=["PDF", "Word", "Excel", "HTML"],
                       state="readonly").pack(fill="x", pady=5)

          def generate_report():
               """دالة إنشاء التقرير الفعلي"""
               try:
                    # تحديد اسم الملف الافتراضي
                    default_filename = f"تقرير_{student['name']}_{get_today_date()}"

                    # فتح حوار حفظ الملف
                    filetypes = [
                         ("PDF Documents", "*.pdf"),
                         ("Word Documents", "*.docx"),
                         ("Excel Files", "*.xlsx"),
                         ("HTML Files", "*.html")
                    ]

                    file_path = filedialog.asksaveasfilename(
                         initialfile=default_filename,
                         defaultextension=".pdf",
                         filetypes=filetypes,
                         title="حفظ التقرير"
                    )

                    if not file_path:
                         return  # تم إلغاء العملية

                    # هنا يتم إنشاء التقرير الفعلي (نستخدم نموذجًا بسيطًا)
                    report_data = {
                         "title": f"تقرير الطالب {student['name']}",
                         "date": get_today_date(),
                         "student_info": {
                              "الاسم": student["name"],
                              "المستوى": student.get("level", "غير محدد"),
                              "السورة الحالية": student.get("current_surah", "غير محدد"),
                              "تاريخ الانضمام": student.get("join_date", "غير محدد")
                         },
                         "recitations": student.get("recitations", []),
                         "attendance": student.get("attendance", [])
                    }

                    # في الواقع هنا يجب استخدام مكتبة مثل reportlab أو fpdf لإنشاء PDF
                    # أو python-docx لملفات Word أو openpyxl لملفات Excel

                    # عرض رسالة نجاح
                    messagebox.showinfo("نجاح", f"تم إنشاء التقرير بنجاح وحفظه في:\n{file_path}")

                    # فتح الملف بعد الإنشاء (اختياري)
                    if messagebox.askyesno("فتح الملف", "هل تريد فتح الملف الآن؟"):
                         import os
                         os.startfile(file_path)

                    report_dialog.destroy()

               except Exception as e:
                    messagebox.showerror("خطأ", f"فشل في إنشاء التقرير:\n{str(e)}")

          # أزرار التحكم
          button_frame = tk.Frame(report_dialog)
          button_frame.pack(fill="x", pady=10)

          tk.Button(button_frame, text="إنشاء التقرير",
                    bg=COLORS["success"], fg="white",
                    command=generate_report).pack(side="right", padx=10)

          tk.Button(button_frame, text="إلغاء",
                    bg=COLORS["danger"], fg="white",
                    command=report_dialog.destroy).pack(side="left", padx=10)

     except Exception as e:
          messagebox.showerror("خطأ", f"حدث خطأ أثناء تحضير التقرير:\n{str(e)}")

# دالة مساعدة لإنشاء ملف PDF (مثال مبسط)
def create_pdf_report(data, filename):
     """إنشاء ملف PDF باستخدام مكتبة reportlab"""
     try:
          from reportlab.lib.pagesizes import A4
          from reportlab.pdfgen import canvas
          from reportlab.lib.utils import ImageReader

          c = canvas.Canvas(filename, pagesize=A4)
          width, height = A4

          # عنوان التقرير
          c.setFont("Helvetica-Bold", 16)
          c.drawString(100, height - 100, data["title"])

          # معلومات الطالب
          c.setFont("Helvetica", 12)
          y_position = height - 150
          for key, value in data["student_info"].items():
               c.drawString(100, y_position, f"{key}: {value}")
               y_position -= 30

          # حفظ الملف
          c.save()
          return True

     except ImportError:
          messagebox.showerror("خطأ", "المكتبة المطلوبة غير مثبتة (reportlab)")
          return False
     except Exception as e:
          messagebox.showerror("خطأ", f"فشل في إنشاء PDF: {str(e)}")
          return False

# ----- لوحة إدارة النظام مع تحسينات -----
def open_main_dashboard_admin(username):
     # إنشاء النافذة الرئيسية
     root = tk.Tk()
     root.title(f"لوحة التحكم - {SYSTEM_NAME} | إدارة النظام")
     root.geometry("1400x900")  # زيادة حجم النافذة لاستيعاب المزيد من المحتوى
     center_window(root, 1400, 900)

     # تحميل أيقونة البرنامج إذا وجدت
     try:
          root.iconbitmap("assets/icon.ico")
     except:
          pass

     # ========== شريط الأدوات العلوي مع تحسينات ==========
     toolbar = tk.Frame(root, bg=COLORS["primary"], height=60)
     toolbar.pack(fill="x")

     # إضافة شعار النظام إذا وجد
     try:
          logo_img = Image.open("assets/logo.png").resize((40, 40))
          logo_photo = ImageTk.PhotoImage(logo_img)
          logo_label = tk.Label(toolbar, image=logo_photo, bg=COLORS["primary"])
          logo_label.image = logo_photo
          logo_label.pack(side="right", padx=10)
     except:
          pass

     # معلومات المستخدم مع صورة البروفايل
     user_frame = tk.Frame(toolbar, bg=COLORS["primary"])
     user_frame.pack(side="right", padx=20)

     try:
          profile_img = Image.open(f"users/{username}/profile.png").resize((30, 30))
          profile_photo = ImageTk.PhotoImage(profile_img)
          profile_label = tk.Label(user_frame, image=profile_photo, bg=COLORS["primary"])
          profile_label.image = profile_photo
          profile_label.pack(side="right")
     except:
          pass

     tk.Label(user_frame, text=f"مرحباً بك، {username}",
              font=FONTS["subtitle"], bg=COLORS["primary"], fg="white").pack(side="right", padx=10)

     # أزرار التحكم في الشريط العلوي
     btn_frame = tk.Frame(toolbar, bg=COLORS["primary"])
     btn_frame.pack(side="left", padx=10)

     def logout():
          if messagebox.askyesno("تأكيد", "هل أنت متأكد من تسجيل الخروج؟"):
               root.destroy()
               login_screen()

     def backup_data():
          try:
               backup_file = filedialog.asksaveasfilename(
                    defaultextension=".nbk",
                    filetypes=[("نسخة احتياطية", "*.nbk")],
                    title="حفظ نسخة احتياطية"
               )
               if backup_file:
                    save_data()
                    messagebox.showinfo("نجاح", "تم إنشاء النسخة الاحتياطية بنجاح")
          except Exception as e:
               messagebox.showerror("خطأ", f"فشل في إنشاء النسخة الاحتياطية: {str(e)}")

     def show_notifications():
          notifications_window = tk.Toplevel(root)
          notifications_window.title("الإشعارات والتنبيهات")
          notifications_window.geometry("500x400")
          center_window(notifications_window, 500, 400)

          # إشعارات افتراضية (يمكن جلبها من قاعدة البيانات)
          notifications = [
               "لديك 3 مراكز تحتاج إلى تحديث بياناتها",
               "5 مستخدمين لم يسجلوا دخول منذ أكثر من شهر",
               "تم تسجيل 10 طلاب جدد هذا الأسبوع"
          ]

          tk.Label(notifications_window, text="الإشعارات الحديثة",
                   font=FONTS["subtitle"], fg=COLORS["primary"]).pack(pady=10)

          for note in notifications:
               tk.Label(notifications_window, text=f"• {note}",
                        font=FONTS["normal"], wraplength=450, justify="right").pack(pady=5, padx=10, anchor="w")

     # أزرار الشريط العلوي
     tk.Button(btn_frame, text="تسجيل الخروج", font=FONTS["small"], bg=COLORS["danger"],
               fg="white", command=logout).pack(side="left", padx=5)
     tk.Button(btn_frame, text="حفظ البيانات", font=FONTS["small"], bg=COLORS["success"],
               fg="white", command=save_data).pack(side="left", padx=5)
     tk.Button(btn_frame, text="نسخة احتياطية", font=FONTS["small"], bg=COLORS["warning"],
               fg="white", command=backup_data).pack(side="left", padx=5)
     tk.Button(btn_frame, text="الإشعارات", font=FONTS["small"], bg=COLORS["info"],
               fg="white", command=show_notifications).pack(side="left", padx=5)

     # زر تحديث البيانات
     def refresh_data():
          load_data()
          messagebox.showinfo("تم", "تم تحديث البيانات بنجاح")

     tk.Button(btn_frame, text="تحديث البيانات", font=FONTS["small"], bg=COLORS["secondary"],
               fg="white", command=refresh_data).pack(side="left", padx=5)

     # ========== تبويبات التحكم مع تحسينات ==========
     style = ttk.Style()
     style.configure("Admin.TNotebook", tabposition="n", padding=[30, 5])
     style.configure("Admin.TNotebook.Tab",
                     font=FONTS["subtitle"],
                     padding=[20, 10],
                     background=COLORS["light"],
                     foreground=COLORS["dark"])

     nb = ttk.Notebook(root, style="Admin.TNotebook")
     nb.pack(fill="both", expand=True, padx=10, pady=5)

     # إنشاء التبويبات مع أيقونات
     tabs = {
          "الرئيسية": {"frame": tk.Frame(nb, bg="#f8f9fa"), "icon": "🏠"},
          "المراكز": {"frame": tk.Frame(nb), "icon": "🏛️"},
          "المستخدمين": {"frame": tk.Frame(nb), "icon": "👥"},
          "الطلاب": {"frame": tk.Frame(nb), "icon": "🧑‍🎓"},  # إعادة تبويب الطلاب
          "التقارير": {"frame": tk.Frame(nb), "icon": "📊"},
          "الإعدادات": {"frame": tk.Frame(nb), "icon": "⚙️"}
     }

     for name, tab_data in tabs.items():
          nb.add(tab_data["frame"], text=f"{tab_data['icon']} {name}")

     # تعبئة التبويبات بالمحتوى مع معالجة الأخطاء
     try:
          show_admin_main(tabs["الرئيسية"]["frame"])
          show_admin_centers(tabs["المراكز"]["frame"], nb)
          show_admin_users(tabs["المستخدمين"]["frame"], nb)
          show_admin_students(tabs["الطلاب"]["frame"])  # إضافة تبويب الطلاب
          show_admin_reports(tabs["التقارير"]["frame"])
          show_admin_settings(tabs["الإعدادات"]["frame"])
     except Exception as e:
          messagebox.showerror("خطأ", f"حدث خطأ أثناء تحميل البيانات: {str(e)}")
          root.destroy()
          return

     # ========== شريط الحالة السفلي مع تحسينات ==========
     status_bar = tk.Frame(root, bg=COLORS["dark"], height=30)
     status_bar.pack(fill="x", side="bottom")

     status_labels = tk.Frame(status_bar, bg=COLORS["dark"])
     status_labels.pack(side="right", padx=10)

     # عرض إحصائيات سريعة
     centers_count = len(CENTERS) if 'CENTERS' in globals() else 0
     users_count = len(USERS) if 'USERS' in globals() else 0
     students_count = len(get_all_students()) if 'TEACHERS' in globals() else 0

     tk.Label(status_labels,
              text=f"المراكز: {centers_count} | المستخدمون: {users_count} | الطلاب: {students_count}",
              font=FONTS["small"], bg=COLORS["dark"], fg="white").pack(side="right", padx=5)

     tk.Label(status_labels,
              text=f"{SYSTEM_NAME} | الإصدار {VERSION} | {get_today_date()} | {get_current_time()}",
              font=FONTS["small"], bg=COLORS["dark"], fg="white").pack(side="right", padx=5)

     # تحديث تلقائي للبيانات كل 5 دقائق
     def auto_refresh():
          try:
               load_data()  # إعادة تحميل البيانات من الملفات
               root.after(300000, auto_refresh)  # 5 دقائق = 300000 مللي ثانية
          except:
               pass

     root.after(300000, auto_refresh)

     root.mainloop()

def show_admin_main(tab):
     tab.pack_propagate(False)

     # تنظيف المحتوى السابق إذا وجد
     for widget in tab.winfo_children():
          widget.destroy()

     # ========== إطار العنوان مع تحسينات ==========
     header = tk.Frame(tab, bg=COLORS["primary"], height=80)
     header.pack(fill="x", pady=(0, 10))

     # إضافة أيقونة إذا وجدت
     try:
          icon_img = Image.open("assets/dashboard_icon.png").resize((50, 50))
          icon_photo = ImageTk.PhotoImage(icon_img)
          icon_label = tk.Label(header, image=icon_photo, bg=COLORS["primary"])
          icon_label.image = icon_photo
          icon_label.pack(side="right", padx=10)
     except:
          pass

     tk.Label(header, text="نظرة عامة على النظام",
              font=FONTS["title"], bg=COLORS["primary"], fg="white").pack(pady=20)

     # زر تحديث البيانات
     def refresh_data():
          load_data()
          show_admin_main(tab)  # إعادة تحميل الصفحة

     refresh_btn = tk.Button(header, text="تحديث البيانات", font=FONTS["small"],
                             bg=COLORS["accent"], fg="white", command=refresh_data)
     refresh_btn.pack(side="left", padx=20)

     # ========== إطار الإحصائيات مع تحسينات ==========
     stats_container = tk.Frame(tab, bg="#f0f0f0", padx=10, pady=10)
     stats_container.pack(fill="x", pady=(0, 20))

     # تحميل البيانات مع التحقق من وجودها
     centers = CENTERS if 'CENTERS' in globals() else []
     managers = [u for u in USERS if u.get("role") == "إدارة المركز"] if 'USERS' in globals() else []
     teachers = TEACHERS if 'TEACHERS' in globals() else []
     students = get_all_students() if 'TEACHERS' in globals() else []

     # حساب التسميعات والحضور
     today_recitations = sum(len([r for r in s.get("recitations", [])
                                  if r.get("date") == get_today_date()])
                             for s in students)

     # حساب نسبة الحضور الفعلية (بدلاً من القيمة الثابتة)
     attendance_records = []
     for teacher in TEACHERS:
          for student in teacher.get("students", []):
               attendance_records.extend(student.get("attendance", []))

     today_attendance = [a for a in attendance_records if a.get("date") == get_today_date()]
     attendance_percentage = "0%"
     if today_attendance:
          present = sum(1 for a in today_attendance if a.get("status") == "حاضر")
          attendance_percentage = f"{round((present / len(today_attendance)) * 100)}%"

     stats = [
          {"label": "عدد المراكز", "value": len(centers), "color": COLORS["primary"], "icon": "🏛️"},
          {"label": "عدد المديرين", "value": len(managers), "color": COLORS["secondary"], "icon": "👨‍💼"},
          {"label": "عدد المعلمين", "value": len(teachers), "color": COLORS["info"], "icon": "👩‍🏫"},
          {"label": "عدد الطلاب", "value": len(students), "color": COLORS["success"], "icon": "🧑‍🎓"},
          {"label": "تسميعات اليوم", "value": today_recitations, "color": COLORS["warning"], "icon": "📖"},
          {"label": "الحضور اليوم", "value": attendance_percentage, "color": COLORS["accent"], "icon": "✅"}
     ]

     for i, stat in enumerate(stats):
          stat_frame = tk.Frame(stats_container, bg=stat["color"], bd=2, relief="ridge",
                                width=200, height=120)
          stat_frame.grid(row=i // 3, column=i % 3, padx=10, pady=10, sticky="nsew")
          stat_frame.grid_propagate(False)

          # إضافة أيقونة
          tk.Label(stat_frame, text=stat["icon"], font=("Arial", 20),
                   bg=stat["color"], fg="white").pack(pady=(10, 0))

          # إضافة التسمية
          tk.Label(stat_frame, text=stat["label"], font=FONTS["small"],
                   bg=stat["color"], fg="white").pack()

          # إضافة القيمة
          tk.Label(stat_frame, text=str(stat["value"]), font=("Cairo", 24, "bold"),
                   bg=stat["color"], fg="white").pack(pady=(5, 10))

          # جعل الإحصائيات قابلة للنقر لعرض التفاصيل
          def show_stat_details(stat_data=stat):
               detail_window = tk.Toplevel()
               detail_window.title(f"تفاصيل {stat_data['label']}")
               detail_window.geometry("500x400")
               center_window(detail_window, 500, 400)

               tk.Label(detail_window, text=f"تفاصيل {stat_data['label']}",
                        font=FONTS["subtitle"], fg=stat_data["color"]).pack(pady=10)

               # يمكن هنا عرض البيانات التفصيلية حسب النوع
               if stat_data["label"] == "عدد المراكز":
                    cols = ("اسم المركز", "المدير", "عدد المعلمين", "عدد الطلاب")
                    tree = ttk.Treeview(detail_window, columns=cols, show="headings")

                    for col in cols:
                         tree.heading(col, text=col)
                         tree.column(col, width=120, anchor="center")

                    for center in centers:
                         center_teachers = len([t for t in teachers if t.get("center") == center.get("name")])
                         center_students = len([s for s in students if s.get("center") == center.get("name")])
                         tree.insert("", "end", values=(
                              center.get("name"),
                              center.get("manager"),
                              center_teachers,
                              center_students
                         ))

                    tree.pack(fill="both", expand=True, padx=10, pady=10)

               # يمكن إضافة حالات أخرى لعرض تفاصيل الإحصائيات الأخرى

          stat_frame.bind("<Button-1>", lambda e, s=stat: show_stat_details(s))
          for child in stat_frame.winfo_children():
               child.bind("<Button-1>", lambda e, s=stat: show_stat_details(s))

     # ========== أحدث الأنشطة مع تحسينات ==========
     activities_container = tk.Frame(tab, bg="white", bd=1, relief="solid")
     activities_container.pack(fill="both", expand=True, padx=20, pady=(0, 20))

     # شريط عنوان الأنشطة
     activities_header = tk.Frame(activities_container, bg=COLORS["light"])
     activities_header.pack(fill="x", pady=(0, 10))

     tk.Label(activities_header, text="أحدث الأنشطة",
              font=FONTS["subtitle"], bg=COLORS["light"]).pack(side="right", padx=10)

     # زر لعرض المزيد من الأنشطة
     def show_all_activities():
          # يمكن تطوير هذه الوظيفة لعرض سجل كامل للأنشطة
          messagebox.showinfo("الأنشطة", "سيتم عرض جميع الأنشطة هنا")

     tk.Button(activities_header, text="عرض الكل", font=FONTS["small"],
               bg=COLORS["primary"], fg="white", command=show_all_activities).pack(side="left", padx=10)

     # إطار التمرير للأنشطة
     canvas = tk.Canvas(activities_container, bg="white", highlightthickness=0)
     scrollbar = ttk.Scrollbar(activities_container, orient="vertical", command=canvas.yview)
     scrollable_frame = tk.Frame(canvas, bg="white")

     scrollable_frame.bind(
          "<Configure>",
          lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
     )

     canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
     canvas.configure(yscrollcommand=scrollbar.set)

     scrollbar.pack(side="left", fill="y")
     canvas.pack(side="right", fill="both", expand=True)

     # جلب الأنشطة الفعلية من البيانات بدلاً من القيم الثابتة
     activities = []
     for user in USERS:
          if user.get("last_login"):
               activities.append((f"تم تسجيل دخول المستخدم '{user['username']}'",
                                  user["last_login"]))

     for center in CENTERS:
          activities.append((f"تم تحديث بيانات مركز '{center['name']}'",
                             center.get("updated_at", "غير معروف")))

     # ترتيب الأنشطة حسب التاريخ (الأحدث أولاً)
     activities.sort(key=lambda x: x[1], reverse=True)
     activities = activities[:10]  # عرض آخر 10 أنشطة فقط

     for activity, time in activities:
          activity_frame = tk.Frame(scrollable_frame, bg="white")
          activity_frame.pack(fill="x", padx=10, pady=5)

          # أيقونة النشاط
          tk.Label(activity_frame, text="•", font=FONTS["normal"],
                   bg="white", fg=COLORS["secondary"]).pack(side="right", padx=5)

          # تفاصيل النشاط
          tk.Label(activity_frame, text=activity, font=FONTS["small"],
                   bg="white", wraplength=400, justify="right").pack(side="right", padx=5)

          # وقت النشاط
          tk.Label(activity_frame, text=time, font=FONTS["small"],
                   bg="white", fg=COLORS["dark"]).pack(side="left", padx=10)

     # ========== مخططات بيانية (بديل مؤقت) ==========
     charts_frame = tk.Frame(tab, bg="white", bd=1, relief="solid")
     charts_frame.pack(fill="x", padx=20, pady=(0, 20))

     tk.Label(charts_frame, text="مخططات إحصائية",
              font=FONTS["subtitle"], bg="white").pack(pady=10)

     # رسم بياني مبسط للإحصائيات
     canvas = tk.Canvas(charts_frame, width=800, height=200, bg="white", highlightthickness=0)
     canvas.pack(pady=10)

     # رسم أعمدة بيانية مبسطة
     max_value = max(len(centers), len(managers), len(teachers), len(students)) or 1
     bar_height = lambda val: (val / max_value) * 180

     colors = [COLORS["primary"], COLORS["secondary"], COLORS["info"], COLORS["success"]]
     stats_labels = ["المراكز", "المديرون", "المعلمون", "الطلاب"]
     stats_values = [len(centers), len(managers), len(teachers), len(students)]

     for i, (label, value, color) in enumerate(zip(stats_labels, stats_values, colors)):
          x1 = 100 + i * 150
          x2 = 180 + i * 150
          canvas.create_rectangle(x1, 200 - bar_height(value), x2, 200,
                                  fill=color, outline="")
          canvas.create_text(x1 + 40, 180 - bar_height(value),
                             text=str(value), font=FONTS["small"])
          canvas.create_text(x1 + 40, 210, text=label, font=FONTS["small"])

def show_admin_centers(tab, nb):
     tab.pack_propagate(False)

     # تنظيف المحتوى السابق إذا وجد
     for widget in tab.winfo_children():
          widget.destroy()

     # ========== شريط الأدوات مع تحسينات ==========
     toolbar = tk.Frame(tab, bg=COLORS["light"], pady=5)
     toolbar.pack(fill="x")

     # العنوان مع أيقونة
     title_frame = tk.Frame(toolbar, bg=COLORS["light"])
     title_frame.pack(side="right", padx=10)
     tk.Label(title_frame, text="🏛️ إدارة المراكز",
              font=FONTS["subtitle"], bg=COLORS["light"]).pack(side="right")

     # مجموعة أزرار التحكم
     btn_frame = tk.Frame(toolbar, bg=COLORS["light"])
     btn_frame.pack(side="left", padx=10)

     def refresh():
          load_data()  # إعادة تحميل البيانات أولاً
          show_admin_centers(tab, nb)

     def add_new_center():
          dialog = tk.Toplevel()
          dialog.title("إضافة مركز جديد")
          dialog.geometry("700x800")
          center_window(dialog, 700, 800)

          # إطار التمرير للنموذج
          main_frame = tk.Frame(dialog)
          main_frame.pack(fill="both", expand=True)

          canvas = tk.Canvas(main_frame)
          scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
          scrollable_frame = tk.Frame(canvas)

          scrollable_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
          canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
          canvas.configure(yscrollcommand=scrollbar.set)

          scrollbar.pack(side="left", fill="y")
          canvas.pack(side="right", fill="both", expand=True)

          tk.Label(scrollable_frame, text="إضافة مركز جديد",
                   font=FONTS["subtitle"], fg=COLORS["primary"]).pack(pady=10)

          # إنشاء حقول النموذج
          new_center_id = f"CEN{len(CENTERS) + 1:03d}"

          fields = [
               {"label": "رقم المركز (تسلسلي)", "value": new_center_id, "type": "entry", "editable": False},
               {"label": "اسم المركز", "value": "", "type": "entry", "required": True},
               {"label": "بريد المركز الإلكتروني", "value": "", "type": "entry", "validation": "email"},
               {"label": "عنوان المركز", "value": "", "type": "entry"},
               {"label": "هاتف المركز", "value": "", "type": "entry", "validation": "phone"},
               {"label": "اسم مدير المركز الكامل", "value": "", "type": "entry", "required": True},
               {"label": "اسم مستخدم المدير", "value": "", "type": "entry", "required": True},
               {"label": "كلمة مرور المدير", "value": "", "type": "entry", "show": "*", "required": True},
               {"label": "تأكيد كلمة المرور", "value": "", "type": "entry", "show": "*", "required": True},
               {"label": "تاريخ ميلاد المدير", "value": "", "type": "date"},
               {"label": "هاتف المدير الشخصي", "value": "", "type": "entry", "validation": "phone"},
               {"label": "الحالة الاجتماعية للمدير", "value": "", "type": "combobox",
                "options": ["أعزب", "متزوج", "مطلق", "أرمل"]}
          ]

          entries = []
          for field in fields:
               frame = tk.Frame(scrollable_frame)
               frame.pack(fill="x", padx=20, pady=5)

               tk.Label(frame, text=field["label"] + ":", font=FONTS["small"],
                        width=25, anchor="e").pack(side="right")

               if field["type"] == "combobox":
                    var = tk.StringVar(value=field["value"])
                    combo = ttk.Combobox(frame, textvariable=var,
                                         values=field["options"],
                                         font=FONTS["small"], state="readonly")
                    combo.pack(fill="x")
                    entries.append(var)
               elif field["type"] == "date":
                    # يمكن استبدال هذا بمكون اختيار تاريخ متقدم
                    entry = ttk.Entry(frame, font=FONTS["small"])
                    entry.insert(0, field["value"])
                    entry.pack(fill="x")
                    entries.append(entry)
               else:
                    show = field.get("show", None)
                    entry = ttk.Entry(frame, font=FONTS["small"], show=show)
                    entry.insert(0, field["value"])
                    if field.get("editable", True) == False:
                         entry.config(state="readonly")
                    entry.pack(fill="x")
                    entries.append(entry)

          def save_center():
               # التحقق من الحقول المطلوبة
               required_fields = [1, 5, 6, 7, 8]  # حقول مطلوبة
               for i in required_fields:
                    if not entries[i].get():
                         messagebox.showerror("خطأ", "جميع الحقول المطلوبة يجب تعبئتها")
                         return

               # التحقق من تطابق كلمتي المرور
               if entries[7].get() != entries[8].get():
                    messagebox.showerror("خطأ", "كلمتا المرور غير متطابقتين")
                    return

               # التحقق من البريد الإلكتروني
               if entries[2].get() and "@" not in entries[2].get():
                    messagebox.showerror("خطأ", "البريد الإلكتروني غير صالح")
                    return

               # التحقق من عدم تكرار اسم المركز
               if any(c.get('name') == entries[1].get() for c in CENTERS):
                    messagebox.showerror("خطأ", "اسم المركز موجود مسبقاً")
                    return

               # التحقق من عدم تكرار اسم المستخدم
               if any(u.get('username') == entries[6].get() for u in USERS):
                    messagebox.showerror("خطأ", "اسم المستخدم موجود مسبقاً")
                    return

               new_center = {
                    "center_id": entries[0].get(),
                    "name": entries[1].get(),
                    "email": entries[2].get(),
                    "address": entries[3].get(),
                    "phone": entries[4].get(),
                    "manager": entries[6].get(),
                    "manager_details": {
                         "full_name": entries[5].get(),
                         "birth_date": entries[9].get(),
                         "personal_phone": entries[10].get(),
                         "marital_status": entries[11].get()
                    },
                    "teachers": [],
                    "created_date": get_today_date(),
                    "status": "نشط"
               }

               new_user = {
                    "user_id": f"MGR{entries[0].get()}",
                    "username": entries[6].get(),
                    "password": entries[7].get(),
                    "role": "إدارة المركز",
                    "full_name": entries[5].get(),
                    "birth_date": entries[9].get(),
                    "phone": entries[10].get(),
                    "marital_status": entries[11].get(),
                    "center": entries[1].get(),
                    "last_login": None,
                    "active": True
               }

               CENTERS.append(new_center)
               USERS.append(new_user)
               save_data()

               dialog.destroy()
               messagebox.showinfo("تم", "تم إضافة المركز ومديره بنجاح")
               refresh()

          btn_frame = tk.Frame(scrollable_frame)
          btn_frame.pack(fill="x", pady=20)

          tk.Button(btn_frame, text="حفظ", font=FONTS["normal"], bg=COLORS["success"],
                    fg="white", command=save_center).pack(side="right", padx=10, ipadx=20)
          tk.Button(btn_frame, text="إلغاء", font=FONTS["normal"], bg=COLORS["danger"],
                    fg="white", command=dialog.destroy).pack(side="left", padx=10, ipadx=20)

     # أزرار الشريط العلوي
     tk.Button(btn_frame, text="إضافة مركز", font=FONTS["small"], bg=COLORS["success"],
               fg="white", command=add_new_center).pack(side="left", padx=5)
     tk.Button(btn_frame, text="تحديث", font=FONTS["small"], bg=COLORS["info"],
               fg="white", command=refresh).pack(side="left", padx=5)

     # زر تصدير البيانات
     def export_data():
          file_path = filedialog.asksaveasfilename(
               defaultextension=".xlsx",
               filetypes=[("Excel Files", "*.xlsx"), ("CSV Files", "*.csv")],
               title="حفظ بيانات المراكز"
          )
          if file_path:
               # هنا سيتم تنفيذ عملية التصدير
               messagebox.showinfo("تم", f"تم تصدير بيانات المراكز إلى {file_path}")

     tk.Button(btn_frame, text="تصدير البيانات", font=FONTS["small"], bg=COLORS["primary"],
               fg="white", command=export_data).pack(side="left", padx=5)

     # ========== إطار البحث والتصفية ==========
     filter_frame = tk.Frame(tab, bg="white", padx=10, pady=10)
     filter_frame.pack(fill="x")

     tk.Label(filter_frame, text="بحث:", font=FONTS["small"], bg="white").pack(side="right")
     search_var = tk.StringVar()
     search_entry = tk.Entry(filter_frame, textvariable=search_var, font=FONTS["small"], width=30)
     search_entry.pack(side="right", padx=5)

     tk.Label(filter_frame, text="تصفية حسب الحالة:", font=FONTS["small"], bg="white").pack(side="right", padx=10)
     status_var = tk.StringVar(value="الكل")
     status_combo = ttk.Combobox(filter_frame, textvariable=status_var,
                                 values=["الكل", "نشط", "غير نشط"],
                                 font=FONTS["small"], state="readonly", width=10)
     status_combo.pack(side="right")

     # ========== جدول المراكز مع تحسينات ==========
     tree_container = tk.Frame(tab)
     tree_container.pack(fill="both", expand=True, padx=10, pady=(0, 10))

     cols = ("رقم المركز", "اسم المركز", "البريد", "الهاتف", "المدير", "عدد المعلمين", "عدد الطلاب", "الحالة")
     tree = ttk.Treeview(tree_container, columns=cols, show="headings", style="Custom.Treeview", height=15)

     # تحديد عرض الأعمدة
     col_widths = {
          "رقم المركز": 100,
          "اسم المركز": 150,
          "البريد": 150,
          "الهاتف": 120,
          "المدير": 150,
          "عدد المعلمين": 100,
          "عدد الطلاب": 100,
          "الحالة": 80
     }

     for col in cols:
          tree.heading(col, text=col)
          tree.column(col, width=col_widths.get(col, 120), anchor="center")

     scroll = ttk.Scrollbar(tree_container, orient="vertical", command=tree.yview)
     tree.configure(yscrollcommand=scroll.set)
     scroll.pack(side="left", fill="y")
     tree.pack(side="right", fill="both", expand=True)

     # تعبئة الجدول بالبيانات
     def load_centers_data():
          for item in tree.get_children():
               tree.delete(item)

          for center in CENTERS:
               center_name = center.get("name", "")
               teachers = get_teachers_in_center(center_name)
               students = get_all_students_in_center(center_name)

               status = center.get("status", "نشط")
               status_color = COLORS["success"] if status == "نشط" else COLORS["danger"]

               tree.insert("", "end", values=(
                    center.get("center_id", "غير معروف"),
                    center_name,
                    center.get("email", ""),
                    center.get("phone", ""),
                    center.get("manager", ""),
                    len(teachers),
                    len(students),
                    status
               ), tags=(status,))

          tree.tag_configure("نشط", background=COLORS["light"])
          tree.tag_configure("غير نشط", background="#ffdddd")

     load_centers_data()

     # وظيفة البحث والتصفية
     def filter_centers(*args):
          search_text = search_var.get().lower()
          status_filter = status_var.get()

          for item in tree.get_children():
               tree.delete(item)

          for center in CENTERS:
               center_name = center.get("name", "")
               status = center.get("status", "نشط")

               if ((status_filter == "الكل" or status == status_filter) and
                        (search_text in center_name.lower() or
                         search_text in center.get("center_id", "").lower() or
                         search_text in center.get("manager", "").lower())):
                    teachers = get_teachers_in_center(center_name)
                    students = get_all_students_in_center(center_name)

                    tree.insert("", "end", values=(
                         center.get("center_id", "غير معروف"),
                         center_name,
                         center.get("email", ""),
                         center.get("phone", ""),
                         center.get("manager", ""),
                         len(teachers),
                         len(students),
                         status
                    ), tags=(status,))

     search_var.trace("w", filter_centers)
     status_var.trace("w", filter_centers)

     # ========== أزرار التحكم السفلية ==========
     control_frame = tk.Frame(tab)
     control_frame.pack(fill="x", pady=(0, 10))

     def edit_selected():
          selected = tree.selection()
          if not selected:
               messagebox.showerror("خطأ", "يرجى اختيار مركز لتعديله")
               return

          center_id = tree.item(selected[0])["values"][0]
          center = next((c for c in CENTERS if c.get('center_id') == center_id), None)
          if not center:
               messagebox.showerror("خطأ", "لم يتم العثور على المركز المحدد")
               return

          dialog = tk.Toplevel()
          dialog.title(f"تعديل مركز {center['name']}")
          dialog.geometry("700x600")
          center_window(dialog, 700, 600)

          # إطار التمرير للنموذج
          main_frame = tk.Frame(dialog)
          main_frame.pack(fill="both", expand=True)

          canvas = tk.Canvas(main_frame)
          scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
          scrollable_frame = tk.Frame(canvas)

          scrollable_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
          canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
          canvas.configure(yscrollcommand=scrollbar.set)

          scrollbar.pack(side="left", fill="y")
          canvas.pack(side="right", fill="both", expand=True)

          tk.Label(scrollable_frame, text=f"تعديل مركز {center['name']}",
                   font=FONTS["subtitle"], fg=COLORS["primary"]).pack(pady=10)

          fields = [
               {"label": "رقم المركز", "value": center["center_id"], "type": "label"},
               {"label": "اسم المركز", "value": center["name"], "type": "entry", "required": True},
               {"label": "بريد المركز الإلكتروني", "value": center.get("email", ""), "type": "entry", "validation": "email"},
               {"label": "عنوان المركز", "value": center.get("address", ""), "type": "entry"},
               {"label": "هاتف المركز", "value": center.get("phone", ""), "type": "entry", "validation": "phone"},
               {"label": "المدير", "value": center.get("manager", ""), "type": "label"},
               {"label": "حالة المركز", "value": center.get("status", "نشط"), "type": "combobox",
                "options": ["نشط", "غير نشط"]}
          ]

          entries = []
          for field in fields:
               frame = tk.Frame(scrollable_frame)
               frame.pack(fill="x", padx=20, pady=5)

               tk.Label(frame, text=field["label"] + ":", font=FONTS["small"],
                        width=25, anchor="e").pack(side="right")

               if field["type"] == "combobox":
                    var = tk.StringVar(value=field["value"])
                    combo = ttk.Combobox(frame, textvariable=var,
                                         values=field["options"],
                                         font=FONTS["small"], state="readonly")
                    combo.pack(fill="x")
                    entries.append(var)
               elif field["type"] == "label":
                    tk.Label(frame, text=field["value"], font=FONTS["small"],
                             anchor="w").pack(fill="x")
               else:
                    entry = ttk.Entry(frame, font=FONTS["small"])
                    entry.insert(0, field["value"])
                    entry.pack(fill="x")
                    entries.append(entry)

          def update_center():
               if not entries[0].get():  # اسم المركز مطلوب
                    messagebox.showerror("خطأ", "اسم المركز مطلوب")
                    return

               # التحقق من البريد الإلكتروني
               if entries[1].get() and "@" not in entries[1].get():
                    messagebox.showerror("خطأ", "البريد الإلكتروني غير صالح")
                    return

               # التحقق من عدم تكرار اسم المركز (إذا تم تغييره)
               new_name = entries[0].get()
               if new_name != center["name"] and any(c.get('name') == new_name for c in CENTERS):
                    messagebox.showerror("خطأ", "اسم المركز موجود مسبقاً")
                    return

               # تحديث بيانات المركز
               center["name"] = new_name
               center["email"] = entries[1].get()
               center["address"] = entries[2].get()
               center["phone"] = entries[3].get()
               center["status"] = entries[4].get()

               # إذا تم تغيير اسم المركز، نحدثه في بيانات المدير
               if new_name != center["name"]:
                    for user in USERS:
                         if user["username"] == center["manager"]:
                              user["center"] = new_name
                              break

               save_data()
               dialog.destroy()
               messagebox.showinfo("تم", "تم تحديث بيانات المركز بنجاح")
               refresh()

          btn_frame = tk.Frame(scrollable_frame)
          btn_frame.pack(fill="x", pady=20)

          tk.Button(btn_frame, text="حفظ التعديلات", font=FONTS["normal"], bg=COLORS["success"],
                    fg="white", command=update_center).pack(side="right", padx=10, ipadx=20)
          tk.Button(btn_frame, text="إلغاء", font=FONTS["normal"], bg=COLORS["danger"],
                    fg="white", command=dialog.destroy).pack(side="left", padx=10, ipadx=20)

     def delete_selected():
          selected = tree.selection()
          if not selected:
               messagebox.showerror("خطأ", "يرجى اختيار مركز لحذفه")
               return

          center_id = tree.item(selected[0])["values"][0]
          center_name = tree.item(selected[0])["values"][1]

          if messagebox.askyesno("تأكيد الحذف",
                                 f"هل أنت متأكد من حذف المركز {center_name}؟\nسيتم حذف جميع بياناته أيضاً"):
               global CENTERS, USERS, TEACHERS

               # حذف المركز
               CENTERS = [c for c in CENTERS if c["center_id"] != center_id]

               # حذف مدير المركز من المستخدمين
               manager_username = next((c["manager"] for c in CENTERS if c["center_id"] == center_id), None)
               if manager_username:
                    USERS = [u for u in USERS if u["username"] != manager_username]

               # حذف المعلمين والطلاب التابعين للمركز
               TEACHERS = [t for t in TEACHERS if t.get("center") != center_name]

               save_data()
               messagebox.showinfo("تم", f"تم حذف المركز {center_name} وبياناته بنجاح")
               refresh()

     def toggle_center_status():
          selected = tree.selection()
          if not selected:
               messagebox.showerror("خطأ", "يرجى اختيار مركز")
               return

          center_id = tree.item(selected[0])["values"][0]
          center = next((c for c in CENTERS if c.get('center_id') == center_id), None)
          if not center:
               messagebox.showerror("خطأ", "لم يتم العثور على المركز المحدد")
               return

          current_status = center.get("status", "نشط")
          new_status = "غير نشط" if current_status == "نشط" else "نشط"

          if messagebox.askyesno("تأكيد", f"هل تريد تغيير حالة المركز إلى {new_status}؟"):
               center["status"] = new_status
               save_data()
               messagebox.showinfo("تم", f"تم تغيير حالة المركز إلى {new_status}")
               refresh()

     # أزرار التحكم
     tk.Button(control_frame, text="تعديل المحدد", font=FONTS["small"], bg=COLORS["warning"],
               fg="white", command=edit_selected).pack(side="right", padx=10)
     tk.Button(control_frame, text="حذف المحدد", font=FONTS["small"], bg=COLORS["danger"],
               fg="white", command=delete_selected).pack(side="right", padx=10)
     tk.Button(control_frame, text="تفعيل/تعطيل", font=FONTS["small"], bg=COLORS["info"],
               fg="white", command=toggle_center_status).pack(side="right", padx=10)
     tk.Button(control_frame, text="عرض التفاصيل", font=FONTS["small"], bg=COLORS["primary"],
               fg="white").pack(side="right", padx=10)

def show_admin_users(tab, nb):
     tab.pack_propagate(False)

     # تنظيف المحتوى السابق إذا وجد
     for widget in tab.winfo_children():
          widget.destroy()

     # ========== شريط الأدوات مع تحسينات ==========
     toolbar = tk.Frame(tab, bg=COLORS["light"], pady=5)
     toolbar.pack(fill="x")

     # العنوان مع أيقونة
     title_frame = tk.Frame(toolbar, bg=COLORS["light"])
     title_frame.pack(side="right", padx=10)
     tk.Label(title_frame, text="👥 إدارة المستخدمين",
              font=FONTS["subtitle"], bg=COLORS["light"]).pack(side="right")

     # مجموعة أزرار التحكم
     btn_frame = tk.Frame(toolbar, bg=COLORS["light"])
     btn_frame.pack(side="left", padx=10)

     def refresh():
          load_data()  # إعادة تحميل البيانات أولاً
          show_admin_users(tab, nb)

     def add_new_user():
          dialog = tk.Toplevel()
          dialog.title("إضافة مستخدم جديد")
          dialog.geometry("600x600")  # زيادة حجم النافذة لاستيعاب المزيد من الحقول
          center_window(dialog, 600, 600)

          # إطار التمرير للنموذج
          main_frame = tk.Frame(dialog)
          main_frame.pack(fill="both", expand=True)

          canvas = tk.Canvas(main_frame)
          scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
          scrollable_frame = tk.Frame(canvas)

          scrollable_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
          canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
          canvas.configure(yscrollcommand=scrollbar.set)

          scrollbar.pack(side="left", fill="y")
          canvas.pack(side="right", fill="both", expand=True)

          tk.Label(scrollable_frame, text="إضافة مستخدم جديد",
                   font=FONTS["subtitle"], fg=COLORS["primary"]).pack(pady=10)

          # إنشاء حقول النموذج مع حقول إضافية
          fields = [
               {"label": "اسم المستخدم", "value": "", "type": "entry", "required": True},
               {"label": "كلمة المرور", "value": "", "type": "entry", "show": "*", "required": True},
               {"label": "تأكيد كلمة المرور", "value": "", "type": "entry", "show": "*", "required": True},
               {"label": "الدور", "value": "", "type": "combobox",
                "options": ["إدارة النظام", "إدارة المركز", "هيئة التدريس"], "required": True},
               {"label": "المركز", "value": "", "type": "combobox",
                "options": [c["name"] for c in CENTERS], "required": False},
               {"label": "الاسم الكامل", "value": "", "type": "entry", "required": False},
               {"label": "البريد الإلكتروني", "value": "", "type": "entry", "validation": "email"},
               {"label": "رقم الهاتف", "value": "", "type": "entry", "validation": "phone"},
               {"label": "الحالة", "value": "نشط", "type": "combobox",
                "options": ["نشط", "غير نشط", "موقوف"], "required": True}
          ]

          entries = []
          for field in fields:
               frame = tk.Frame(scrollable_frame)
               frame.pack(fill="x", padx=20, pady=5)

               tk.Label(frame, text=field["label"] + ":", font=FONTS["small"],
                        width=20, anchor="e").pack(side="right")

               if field["type"] == "combobox":
                    var = tk.StringVar(value=field["value"])
                    combo = ttk.Combobox(frame, textvariable=var,
                                         values=field["options"],
                                         font=FONTS["small"], state="readonly")
                    combo.pack(fill="x")
                    entries.append(var)
               else:
                    show = field.get("show", None)
                    entry = ttk.Entry(frame, font=FONTS["small"], show=show)
                    entry.insert(0, field["value"])
                    entry.pack(fill="x")
                    entries.append(entry)

          def save_user():
               # التحقق من الحقول المطلوبة
               required_fields = [0, 1, 2, 3]  # حقول مطلوبة
               for i in required_fields:
                    if not entries[i].get():
                         messagebox.showerror("خطأ", "جميع الحقول المطلوبة يجب تعبئتها")
                         return

               # التحقق من تطابق كلمتي المرور
               if entries[1].get() != entries[2].get():
                    messagebox.showerror("خطأ", "كلمتا المرور غير متطابقتين")
                    return

               username = entries[0].get()
               password = entries[1].get()
               role = entries[3].get()
               center = entries[4].get() if role != "إدارة النظام" else None

               # التحقق من عدم تكرار اسم المستخدم
               if any(u['username'] == username for u in USERS):
                    messagebox.showerror("خطأ", "اسم المستخدم موجود مسبقاً")
                    return

               # التحقق من البريد الإلكتروني إذا تم إدخاله
               if entries[6].get() and "@" not in entries[6].get():
                    messagebox.showerror("خطأ", "البريد الإلكتروني غير صالح")
                    return

               # إنشاء معرف فريد للمستخدم
               user_id = f"USR{len(USERS) + 1:04d}"

               new_user = {
                    "user_id": user_id,
                    "username": username,
                    "password": password,
                    "role": role,
                    "full_name": entries[5].get(),
                    "email": entries[6].get(),
                    "phone": entries[7].get(),
                    "center": center,
                    "status": entries[8].get(),
                    "created_at": f"{get_today_date()} {get_current_time()}",
                    "last_login": None
               }

               USERS.append(new_user)

               # إذا كان المستخدم معلمًا، إضافته إلى قائمة المعلمين
               if role == "هيئة التدريس":
                    TEACHERS.append({
                         "teacher_id": f"TEA{len(TEACHERS) + 1:03d}",
                         "username": username,
                         "full_name": entries[5].get(),
                         "center": center,
                         "students": [],
                         "status": entries[8].get()
                    })

               save_data()
               dialog.destroy()
               messagebox.showinfo("تم", f"تم إضافة المستخدم {username} بنجاح")
               refresh()

          btn_frame = tk.Frame(scrollable_frame)
          btn_frame.pack(fill="x", pady=20)

          tk.Button(btn_frame, text="حفظ", font=FONTS["normal"], bg=COLORS["success"],
                    fg="white", command=save_user).pack(side="right", padx=10, ipadx=20)
          tk.Button(btn_frame, text="إلغاء", font=FONTS["normal"], bg=COLORS["danger"],
                    fg="white", command=dialog.destroy).pack(side="left", padx=10, ipadx=20)

     # أزرار الشريط العلوي
     tk.Button(btn_frame, text="إضافة مستخدم", font=FONTS["small"], bg=COLORS["success"],
               fg="white", command=add_new_user).pack(side="left", padx=5)
     tk.Button(btn_frame, text="تحديث", font=FONTS["small"], bg=COLORS["info"],
               fg="white", command=refresh).pack(side="left", padx=5)

     # زر تصدير البيانات
     def export_data():
          file_path = filedialog.asksaveasfilename(
               defaultextension=".xlsx",
               filetypes=[("Excel Files", "*.xlsx"), ("CSV Files", "*.csv")],
               title="حفظ بيانات المستخدمين"
          )
          if file_path:
               # هنا سيتم تنفيذ عملية التصدير
               messagebox.showinfo("تم", f"تم تصدير بيانات المستخدمين إلى {file_path}")

     tk.Button(btn_frame, text="تصدير البيانات", font=FONTS["small"], bg=COLORS["primary"],
               fg="white", command=export_data).pack(side="left", padx=5)

     # ========== إطار البحث والتصفية ==========
     filter_frame = tk.Frame(tab, bg="white", padx=10, pady=10)
     filter_frame.pack(fill="x")

     tk.Label(filter_frame, text="بحث:", font=FONTS["small"], bg="white").pack(side="right")
     search_var = tk.StringVar()
     search_entry = tk.Entry(filter_frame, textvariable=search_var, font=FONTS["small"], width=30)
     search_entry.pack(side="right", padx=5)

     tk.Label(filter_frame, text="تصفية حسب الدور:", font=FONTS["small"], bg="white").pack(side="right", padx=10)
     role_var = tk.StringVar(value="الكل")
     role_combo = ttk.Combobox(filter_frame, textvariable=role_var,
                               values=["الكل", "إدارة النظام", "إدارة المركز", "هيئة التدريس"],
                               font=FONTS["small"], state="readonly", width=15)
     role_combo.pack(side="right")

     tk.Label(filter_frame, text="تصفية حسب الحالة:", font=FONTS["small"], bg="white").pack(side="right", padx=10)
     status_var = tk.StringVar(value="الكل")
     status_combo = ttk.Combobox(filter_frame, textvariable=status_var,
                                 values=["الكل", "نشط", "غير نشط", "موقوف"],
                                 font=FONTS["small"], state="readonly", width=15)
     status_combo.pack(side="right")

     # ========== جدول المستخدمين مع تحسينات ==========
     tree_container = tk.Frame(tab)
     tree_container.pack(fill="both", expand=True, padx=10, pady=(0, 10))

     cols = ("اسم المستخدم", "الدور", "المركز", "الحالة", "آخر دخول", "تاريخ الإنشاء")
     tree = ttk.Treeview(tree_container, columns=cols, show="headings", style="Custom.Treeview", height=15)

     # تحديد عرض الأعمدة
     col_widths = {
          "اسم المستخدم": 120,
          "الدور": 120,
          "المركز": 150,
          "الحالة": 80,
          "آخر دخول": 120,
          "تاريخ الإنشاء": 120
     }

     for col in cols:
          tree.heading(col, text=col)
          tree.column(col, width=col_widths.get(col, 120), anchor="center")

     scroll = ttk.Scrollbar(tree_container, orient="vertical", command=tree.yview)
     tree.configure(yscrollcommand=scroll.set)
     scroll.pack(side="left", fill="y")
     tree.pack(side="right", fill="both", expand=True)

     # تعبئة الجدول بالبيانات
     def load_users_data():
          for item in tree.get_children():
               tree.delete(item)

          for user in USERS:
               status = user.get("status", "نشط")
               status_color = COLORS["success"] if status == "نشط" else COLORS["danger"] if status == "موقوف" else COLORS[
                    "warning"]

               tree.insert("", "end", values=(
                    user["username"],
                    user["role"],
                    user.get("center", ""),
                    status,
                    user.get("last_login", "لم يسجل دخول"),
                    user.get("created_at", "غير معروف")
               ), tags=(status,))

          tree.tag_configure("نشط", background=COLORS["light"])
          tree.tag_configure("غير نشط", background="#ffebee")
          tree.tag_configure("موقوف", background="#ffcdd2")

     load_users_data()

     # وظيفة البحث والتصفية
     def filter_users(*args):
          search_text = search_var.get().lower()
          role_filter = role_var.get()
          status_filter = status_var.get()

          for item in tree.get_children():
               tree.delete(item)

          for user in USERS:
               if ((role_filter == "الكل" or user["role"] == role_filter) and
                        (status_filter == "الكل" or user.get("status", "نشط") == status_filter) and
                        (search_text in user["username"].lower() or
                         search_text in user.get("full_name", "").lower() or
                         search_text in user.get("email", "").lower())):
                    tree.insert("", "end", values=(
                         user["username"],
                         user["role"],
                         user.get("center", ""),
                         user.get("status", "نشط"),
                         user.get("last_login", "لم يسجل دخول"),
                         user.get("created_at", "غير معروف")
                    ), tags=(user.get("status", "نشط"),))

     search_var.trace("w", filter_users)
     role_var.trace("w", filter_users)
     status_var.trace("w", filter_users)

     # ========== أزرار التحكم السفلية ==========
     control_frame = tk.Frame(tab)
     control_frame.pack(fill="x", pady=10)

     def edit_selected():
          selected = tree.selection()
          if not selected:
               messagebox.showerror("خطأ", "يرجى اختيار مستخدم لتعديله")
               return

          username = tree.item(selected[0])["values"][0]
          user = next((u for u in USERS if u["username"] == username), None)
          if not user:
               messagebox.showerror("خطأ", "لم يتم العثور على المستخدم المحدد")
               return

          dialog = tk.Toplevel()
          dialog.title(f"تعديل بيانات المستخدم {username}")
          dialog.geometry("600x600")
          center_window(dialog, 600, 600)

          # إطار التمرير للنموذج
          main_frame = tk.Frame(dialog)
          main_frame.pack(fill="both", expand=True)

          canvas = tk.Canvas(main_frame)
          scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
          scrollable_frame = tk.Frame(canvas)

          scrollable_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
          canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
          canvas.configure(yscrollcommand=scrollbar.set)

          scrollbar.pack(side="left", fill="y")
          canvas.pack(side="right", fill="both", expand=True)

          tk.Label(scrollable_frame, text=f"تعديل بيانات المستخدم {username}",
                   font=FONTS["subtitle"], fg=COLORS["primary"]).pack(pady=10)

          fields = [
               {"label": "اسم المستخدم", "value": user["username"], "type": "entry", "editable": False},
               {"label": "كلمة المرور", "value": user["password"], "type": "entry", "show": "*", "required": True},
               {"label": "الدور", "value": user["role"], "type": "combobox",
                "options": ["إدارة النظام", "إدارة المركز", "هيئة التدريس"], "required": True},
               {"label": "المركز", "value": user.get("center", ""), "type": "combobox",
                "options": [c["name"] for c in CENTERS], "required": False},
               {"label": "الاسم الكامل", "value": user.get("full_name", ""), "type": "entry", "required": False},
               {"label": "البريد الإلكتروني", "value": user.get("email", ""), "type": "entry", "validation": "email"},
               {"label": "رقم الهاتف", "value": user.get("phone", ""), "type": "entry", "validation": "phone"},
               {"label": "الحالة", "value": user.get("status", "نشط"), "type": "combobox",
                "options": ["نشط", "غير نشط", "موقوف"], "required": True}
          ]

          entries = []
          for field in fields:
               frame = tk.Frame(scrollable_frame)
               frame.pack(fill="x", padx=20, pady=5)

               tk.Label(frame, text=field["label"] + ":", font=FONTS["small"],
                        width=20, anchor="e").pack(side="right")

               if field["type"] == "combobox":
                    var = tk.StringVar(value=field["value"])
                    combo = ttk.Combobox(frame, textvariable=var,
                                         values=field["options"],
                                         font=FONTS["small"], state="readonly")
                    combo.pack(fill="x")
                    entries.append(var)
               else:
                    show = field.get("show", None)
                    entry = ttk.Entry(frame, font=FONTS["small"], show=show)
                    entry.insert(0, field["value"])
                    if not field.get("editable", True):
                         entry.config(state="readonly")
                    entry.pack(fill="x")
                    entries.append(entry)

          def update_user():
               # التحقق من الحقول المطلوبة
               required_fields = [0, 1, 2]  # حقول مطلوبة
               for i in required_fields:
                    if not entries[i].get():
                         messagebox.showerror("خطأ", "جميع الحقول المطلوبة يجب تعبئتها")
                         return

               # التحقق من البريد الإلكتروني إذا تم إدخاله
               if entries[5].get() and "@" not in entries[5].get():
                    messagebox.showerror("خطأ", "البريد الإلكتروني غير صالح")
                    return

               # تحديث بيانات المستخدم
               user.update({
                    "password": entries[1].get(),
                    "role": entries[2].get(),
                    "center": entries[3].get() if entries[2].get() != "إدارة النظام" else None,
                    "full_name": entries[4].get(),
                    "email": entries[5].get(),
                    "phone": entries[6].get(),
                    "status": entries[7].get(),
                    "updated_at": f"{get_today_date()} {get_current_time()}"
               })

               # إذا كان المستخدم معلمًا، تحديث بياناته في قائمة المعلمين
               if user["role"] == "هيئة التدريس":
                    teacher = next((t for t in TEACHERS if t["username"] == user["username"]), None)
                    if teacher:
                         teacher.update({
                              "center": user["center"],
                              "status": user["status"]
                         })

               save_data()
               dialog.destroy()
               messagebox.showinfo("تم", "تم تحديث بيانات المستخدم بنجاح")
               refresh()

          btn_frame = tk.Frame(scrollable_frame)
          btn_frame.pack(fill="x", pady=20)

          tk.Button(btn_frame, text="حفظ التعديلات", font=FONTS["normal"], bg=COLORS["success"],
                    fg="white", command=update_user).pack(side="right", padx=10, ipadx=20)
          tk.Button(btn_frame, text="إلغاء", font=FONTS["normal"], bg=COLORS["danger"],
                    fg="white", command=dialog.destroy).pack(side="left", padx=10, ipadx=20)

     def delete_selected():
          global USERS, TEACHERS
          selected = tree.selection()
          if not selected:
               messagebox.showerror("خطأ", "يرجى اختيار مستخدم لحذفه")
               return

          username = tree.item(selected[0])["values"][0]
          user = next((u for u in USERS if u["username"] == username), None)
          if not user:
               messagebox.showerror("خطأ", "لم يتم العثور على المستخدم المحدد")
               return

          if user["role"] == "إدارة النظام":
               messagebox.showerror("خطأ", "لا يمكن حذف مستخدم إدارة النظام")
               return

          if messagebox.askyesno("تأكيد الحذف",
                                 f"هل أنت متأكد من حذف المستخدم {username}؟\nسيتم حذف جميع بياناته أيضاً"):


               # حذف المستخدم
               USERS = [u for u in USERS if u["username"] != username]

               # إذا كان معلم، حذفه من قائمة المعلمين
               if user["role"] == "هيئة التدريس":
                    TEACHERS = [t for t in TEACHERS if t["username"] != username]

               save_data()
               messagebox.showinfo("تم", f"تم حذف المستخدم {username} بنجاح")
               refresh()

     def toggle_user_status():
          selected = tree.selection()
          if not selected:
               messagebox.showerror("خطأ", "يرجى اختيار مستخدم")
               return

          username = tree.item(selected[0])["values"][0]
          user = next((u for u in USERS if u["username"] == username), None)
          if not user:
               messagebox.showerror("خطأ", "لم يتم العثور على المستخدم المحدد")
               return

          current_status = user.get("status", "نشط")
          new_status = "موقوف" if current_status == "نشط" else "نشط"

          if messagebox.askyesno("تأكيد", f"هل تريد تغيير حالة المستخدم إلى {new_status}؟"):
               user["status"] = new_status

               # إذا كان المستخدم معلمًا، تحديث حالته في قائمة المعلمين
               if user["role"] == "هيئة التدريس":
                    teacher = next((t for t in TEACHERS if t["username"] == user["username"]), None)
                    if teacher:
                         teacher["status"] = new_status

               save_data()
               messagebox.showinfo("تم", f"تم تغيير حالة المستخدم إلى {new_status}")
               refresh()

     def reset_password():
          selected = tree.selection()
          if not selected:
               messagebox.showerror("خطأ", "يرجى اختيار مستخدم")
               return

          username = tree.item(selected[0])["values"][0]
          user = next((u for u in USERS if u["username"] == username), None)
          if not user:
               messagebox.showerror("خطأ", "لم يتم العثور على المستخدم المحدد")
               return

          new_password = simpledialog.askstring("إعادة تعيين كلمة المرور",
                                                f"أدخل كلمة المرور الجديدة للمستخدم {username}:",
                                                parent=tab, show="*")
          if new_password:
               if len(new_password) < 4:
                    messagebox.showerror("خطأ", "كلمة المرور يجب أن تكون 4 أحرف على الأقل")
                    return

               user["password"] = new_password
               save_data()
               messagebox.showinfo("تم", "تم تحديث كلمة المرور بنجاح")

     # أزرار التحكم
     tk.Button(control_frame, text="تعديل المحدد", font=FONTS["small"], bg=COLORS["warning"],
               fg="white", command=edit_selected).pack(side="right", padx=10)
     tk.Button(control_frame, text="حذف المحدد", font=FONTS["small"], bg=COLORS["danger"],
               fg="white", command=delete_selected).pack(side="right", padx=10)
     tk.Button(control_frame, text="تفعيل/تعطيل", font=FONTS["small"], bg=COLORS["info"],
               fg="white", command=toggle_user_status).pack(side="right", padx=10)
     tk.Button(control_frame, text="إعادة تعيين كلمة المرور", font=FONTS["small"], bg=COLORS["primary"],
               fg="white", command=reset_password).pack(side="right", padx=10)

def show_admin_reports(tab):
     tab.pack_propagate(False)

     # تنظيف المحتوى السابق إذا وجد
     for widget in tab.winfo_children():
          widget.destroy()

     # ========== شريط الأدوات مع تحسينات ==========
     toolbar = tk.Frame(tab, bg=COLORS["light"], pady=5)
     toolbar.pack(fill="x")

     # العنوان مع أيقونة
     title_frame = tk.Frame(toolbar, bg=COLORS["light"])
     title_frame.pack(side="right", padx=10)
     tk.Label(title_frame, text="📊 التقارير والإحصائيات",
              font=FONTS["subtitle"], bg=COLORS["light"]).pack(side="right")

     # مجموعة أزرار التحكم
     btn_frame = tk.Frame(toolbar, bg=COLORS["light"])
     btn_frame.pack(side="left", padx=10)

     def refresh():
          show_admin_reports(tab)

     # ========== تبويبات التقارير مع تحسينات ==========
     report_nb = ttk.Notebook(tab)
     report_nb.pack(fill="both", expand=True)

     # تبويب تقارير المراكز
     centers_frame = tk.Frame(report_nb)
     report_nb.add(centers_frame, text="🏛️ تقارير المراكز")

     # إضافة خيارات التصفية
     filter_frame = tk.Frame(centers_frame, bg="white", padx=10, pady=10)
     filter_frame.pack(fill="x")

     tk.Label(filter_frame, text="ترتيب حسب:", font=FONTS["small"], bg="white").pack(side="right")
     sort_var = tk.StringVar(value="عدد الطلاب")
     sort_combo = ttk.Combobox(filter_frame, textvariable=sort_var,
                               values=["عدد الطلاب", "عدد المعلمين", "اسم المركز"],
                               font=FONTS["small"], state="readonly", width=15)
     sort_combo.pack(side="right", padx=5)

     tk.Label(filter_frame, text="تصفية حسب النشاط:", font=FONTS["small"], bg="white").pack(side="right", padx=10)
     activity_var = tk.StringVar(value="الكل")
     activity_combo = ttk.Combobox(filter_frame, textvariable=activity_var,
                                   values=["الكل", "نشط", "غير نشط"],
                                   font=FONTS["small"], state="readonly", width=10)
     activity_combo.pack(side="right")

     # جدول تقارير المراكز
     tree_frame = tk.Frame(centers_frame)
     tree_frame.pack(fill="both", expand=True, padx=10, pady=10)

     cols = ("المركز", "المدير", "عدد الطلاب", "عدد المعلمين",
             "تسميعات اليوم", "الحضور %", "الحالة")
     tree = ttk.Treeview(tree_frame, columns=cols, show="headings", style="Custom.Treeview", height=10)

     # تحديد عرض الأعمدة
     col_widths = {
          "المركز": 150,
          "المدير": 120,
          "عدد الطلاب": 80,
          "عدد المعلمين": 80,
          "تسميعات اليوم": 100,
          "الحضور %": 80,
          "الحالة": 80
     }

     for col in cols:
          tree.heading(col, text=col)
          tree.column(col, width=col_widths.get(col, 100), anchor="center")

     scroll = ttk.Scrollbar(tree_frame, orient="vertical", command=tree.yview)
     tree.configure(yscrollcommand=scroll.set)
     scroll.pack(side="left", fill="y")
     tree.pack(side="right", fill="both", expand=True)

     # تعبئة الجدول بالبيانات
     def load_centers_data():
          for item in tree.get_children():
               tree.delete(item)

          centers_data = []
          for center in CENTERS:
               center_name = center.get("name", "")
               teachers = get_teachers_in_center(center_name)
               students = get_all_students_in_center(center_name)

               # حساب تسميعات اليوم
               today_rec = sum(len([r for r in s.get("recitations", [])
                                    if r.get("date") == get_today_date()])
                               for s in students)

               # حساب نسبة الحضور (مثال مبسط)
               attendance_records = []
               for teacher in TEACHERS:
                    for student in teacher.get("students", []):
                         attendance_records.extend(student.get("attendance", []))

               today_attendance = [a for a in attendance_records if a.get("date") == get_today_date()]
               attendance_percentage = 0
               if today_attendance:
                    present = sum(1 for a in today_attendance if a.get("status") == "حاضر")
                    attendance_percentage = round((present / len(today_attendance)) * 100)

               centers_data.append({
                    "name": center_name,
                    "manager": center.get("manager", ""),
                    "students_count": len(students),
                    "teachers_count": len(teachers),
                    "today_recitations": today_rec,
                    "attendance": f"{attendance_percentage}%",
                    "status": center.get("status", "نشط")
               })

          # التصفية والترتيب
          if activity_var.get() != "الكل":
               centers_data = [c for c in centers_data if c["status"] == activity_var.get()]

          sort_key = {
               "عدد الطلاب": lambda x: x["students_count"],
               "عدد المعلمين": lambda x: x["teachers_count"],
               "اسم المركز": lambda x: x["name"]
          }.get(sort_var.get(), lambda x: x["students_count"])

          centers_data.sort(key=sort_key, reverse=True)

          # إضافة البيانات إلى الجدول
          for center in centers_data:
               status_color = COLORS["success"] if center["status"] == "نشط" else COLORS["danger"]
               tree.insert("", "end", values=(
                    center["name"],
                    center["manager"],
                    center["students_count"],
                    center["teachers_count"],
                    center["today_recitations"],
                    center["attendance"],
                    center["status"]
               ), tags=(center["status"],))

          tree.tag_configure("نشط", background=COLORS["light"])
          tree.tag_configure("غير نشط", background="#ffdddd")

     load_centers_data()

     # تبويب إحصائيات عامة
     stats_frame = tk.Frame(report_nb)
     report_nb.add(stats_frame, text="📈 إحصائيات عامة")

     # مخططات بيانية (بديل مؤقت)
     canvas_frame = tk.Frame(stats_frame)
     canvas_frame.pack(fill="both", expand=True, padx=20, pady=20)

     # رسم بياني للإحصائيات
     canvas = tk.Canvas(canvas_frame, width=800, height=400, bg="white")
     canvas.pack()

     # بيانات الإحصائيات
     total_students = len(get_all_students())
     total_teachers = len(TEACHERS)
     total_centers = len(CENTERS)
     active_centers = len([c for c in CENTERS if c.get("status") == "نشط"])

     # رسم الأعمدة البيانية
     max_value = max(total_students, total_teachers, total_centers, active_centers) or 1
     bar_height = lambda val: (val / max_value) * 300

     stats = [
          {"label": "الطلاب", "value": total_students, "color": COLORS["primary"]},
          {"label": "المعلمون", "value": total_teachers, "color": COLORS["secondary"]},
          {"label": "المراكز", "value": total_centers, "color": COLORS["info"]},
          {"label": "مراكز نشطة", "value": active_centers, "color": COLORS["success"]}
     ]

     for i, stat in enumerate(stats):
          x1 = 100 + i * 180
          x2 = 250 + i * 180
          canvas.create_rectangle(x1, 400 - bar_height(stat["value"]), x2, 400,
                                  fill=stat["color"], outline="")
          canvas.create_text(x1 + 75, 380 - bar_height(stat["value"]),
                             text=str(stat["value"]), font=FONTS["small"])
          canvas.create_text(x1 + 75, 420, text=stat["label"], font=FONTS["small"])

     # ========== أزرار التحكم مع تحسينات ==========
     control_frame = tk.Frame(tab)
     control_frame.pack(fill="x", pady=10)

     def print_report():
          # في الواقع، هنا سيتم إنشاء تقرير PDF وجاهز للطباعة
          report_data = []
          for item in tree.get_children():
               report_data.append(tree.item(item)["values"])

          # يمكن استبدال هذا بإنشاء ملف PDF فعلي باستخدام مكتبة مثل reportlab
          messagebox.showinfo("طباعة التقرير",
                              "تم تجهيز التقرير للطباعة\nعدد السجلات: " + str(len(report_data)))

     def export_report():
          file_path = filedialog.asksaveasfilename(
               defaultextension=".xlsx",
               filetypes=[("Excel Files", "*.xlsx"), ("PDF Files", "*.pdf"), ("CSV Files", "*.csv")],
               title="حفظ التقرير"
          )

          if file_path:
               # في الواقع، هنا سيتم تصدير البيانات إلى الملف المحدد
               # يمكن استخدام مكتبة مثل pandas أو openpyxl لإنشاء ملف Excel
               # أو reportlab لإنشاء PDF

               # مثال مبسط:
               if file_path.endswith(".xlsx"):
                    messagebox.showinfo("تصدير", "سيتم تصدير البيانات إلى ملف Excel")
               elif file_path.endswith(".pdf"):
                    messagebox.showinfo("تصدير", "سيتم تصدير البيانات إلى ملف PDF")
               else:
                    messagebox.showinfo("تصدير", "سيتم تصدير البيانات إلى ملف CSV")

     def generate_detailed_report():
          dialog = tk.Toplevel()
          dialog.title("تقرير مفصل")
          dialog.geometry("800x600")
          center_window(dialog, 800, 600)

          # إطار التمرير
          main_frame = tk.Frame(dialog)
          main_frame.pack(fill="both", expand=True)

          canvas = tk.Canvas(main_frame)
          scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
          scrollable_frame = tk.Frame(canvas)

          scrollable_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
          canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
          canvas.configure(yscrollcommand=scrollbar.set)

          scrollbar.pack(side="left", fill="y")
          canvas.pack(side="right", fill="both", expand=True)

          # محتوى التقرير المفصل
          tk.Label(scrollable_frame, text="تقرير أداء المراكز",
                   font=FONTS["title"], fg=COLORS["primary"]).pack(pady=20)

          # إحصائيات عامة
          stats_frame = tk.Frame(scrollable_frame, bd=1, relief="solid", padx=10, pady=10)
          stats_frame.pack(fill="x", pady=10)

          tk.Label(stats_frame, text="الإحصائيات العامة",
                   font=FONTS["subtitle"], fg=COLORS["dark"]).pack(anchor="w")

          stats_text = f"""
        عدد المراكز: {len(CENTERS)} (نشطة: {active_centers})
        عدد المعلمين: {total_teachers}
        عدد الطلاب: {total_students}
        متوسط الطلاب لكل مركز: {round(total_students / max(1, total_centers))}
        متوسط الطلاب لكل معلم: {round(total_students / max(1, total_teachers))}
        """
          tk.Label(stats_frame, text=stats_text,
                   font=FONTS["normal"], justify="right").pack(anchor="e")

          # تفاصيل كل مركز
          for center in CENTERS:
               center_frame = tk.Frame(scrollable_frame, bd=1, relief="solid", padx=10, pady=10)
               center_frame.pack(fill="x", pady=5)

               tk.Label(center_frame, text=f"مركز {center['name']}",
                        font=FONTS["subtitle"], fg=COLORS["secondary"]).pack(anchor="w")

               teachers = get_teachers_in_center(center["name"])
               students = get_all_students_in_center(center["name"])

               center_stats = f"""
            المدير: {center.get("manager", "غير معروف")}
            عدد المعلمين: {len(teachers)}
            عدد الطلاب: {len(students)}
            حالة المركز: {center.get("status", "نشط")}
            تاريخ الإنشاء: {center.get("created_date", "غير معروف")}
            """
               tk.Label(center_frame, text=center_stats,
                        font=FONTS["small"], justify="right").pack(anchor="e")

     # أزرار التحكم
     tk.Button(control_frame, text="طباعة التقرير", font=FONTS["small"], bg=COLORS["primary"],
               fg="white", command=print_report).pack(side="right", padx=10)
     tk.Button(control_frame, text="تصدير التقرير", font=FONTS["small"], bg=COLORS["success"],
               fg="white", command=export_report).pack(side="right", padx=10)
     tk.Button(control_frame, text="تقرير مفصل", font=FONTS["small"], bg=COLORS["info"],
               fg="white", command=generate_detailed_report).pack(side="right", padx=10)
     tk.Button(control_frame, text="تحديث البيانات", font=FONTS["small"], bg=COLORS["secondary"],
               fg="white", command=refresh).pack(side="left", padx=10)

def show_admin_students(tab):
     tab.pack_propagate(False)

     # شريط الأدوات
     toolbar = tk.Frame(tab, bg=COLORS["light"], pady=5)
     toolbar.pack(fill="x")
     tk.Label(toolbar, text="إدارة الطلاب", font=FONTS["subtitle"], bg=COLORS["light"]).pack(side="right", padx=10)
     def refresh():
          show_admin_students(tab)
     tk.Button(toolbar, text="تحديث", font=FONTS["small"], bg=COLORS["info"], fg="white", command=refresh).pack(side="left",
                                                                                                                padx=10)
     # إطار البحث والتصفية
     filter_frame = tk.Frame(tab, bg="white", padx=10, pady=10)
     filter_frame.pack(fill="x")
     tk.Label(filter_frame, text="بحث:", font=FONTS["small"], bg="white").pack(side="right")
     search_var = tk.StringVar()
     search_entry = tk.Entry(filter_frame, textvariable=search_var, font=FONTS["small"], width=30)
     search_entry.pack(side="right", padx=5)
     tk.Label(filter_frame, text="تصفية حسب المركز:", font=FONTS["small"], bg="white").pack(side="right", padx=10)
     center_var = tk.StringVar()
     center_combo = ttk.Combobox(filter_frame, textvariable=center_var, values=["الكل"] + [c["name"] for c in CENTERS],
                                 font=FONTS["small"], state="readonly", width=20)
     center_combo.current(0)
     center_combo.pack(side="right")

     # جدول الطلاب
     tree_frame = tk.Frame(tab)
     tree_frame.pack(fill="both", expand=True, padx=10, pady=10)
     cols = ("الاسم", "المستوى", "السورة الحالية", "المعلم", "المركز", "الأداء", "تاريخ الانضمام")
     tree = ttk.Treeview(tree_frame, columns=cols, show="headings", style="Custom.Treeview")
     for col in cols:
          tree.heading(col, text=col)
          tree.column(col, width=120, anchor="center")
     scroll = ttk.Scrollbar(tree_frame, orient="vertical", command=tree.yview)
     tree.configure(yscrollcommand=scroll.set)
     scroll.pack(side="left", fill="y")
     tree.pack(side="right", fill="both", expand=True)
     students = get_all_students()
     for s in students:
          tree.insert("", "end", values=(
               s["name"],
               s["level"],
               s["current_surah"],
               s["teacher"],
               s["center"],
               s["performance"],
               s.get("join_date", "")
          ))

     def search_students(*args):
          search_text = search_var.get().lower()
          center_filter = center_var.get()
          for item in tree.get_children():
               tree.delete(item)
          for s in students:
               if (search_text in s["name"].lower() or search_text in s["teacher"].lower()) and \
                        (center_filter == "الكل" or s["center"] == center_filter):
                    tree.insert("", "end", values=(
                         s["name"],
                         s["level"],
                         s["current_surah"],
                         s["teacher"],
                         s["center"],
                         s["performance"],
                         s.get("join_date", "")
                    ))

     search_var.trace("w", search_students)
     center_var.trace("w", search_students)
     def open_selected():
          selected = tree.selection()
          if not selected:
               messagebox.showerror("خطأ", "يرجى اختيار طالب")
               return

          student_name = tree.item(selected[0])["values"][0]
          teacher_name = tree.item(selected[0])["values"][3]
          teacher = next(t for t in TEACHERS if t["username"] == teacher_name)
          student = next(s for s in teacher["students"] if s["name"] == student_name)
          open_student_file(student)

     # أزرار التحكم
     control_frame = tk.Frame(tab)
     control_frame.pack(fill="x", pady=10)
     tk.Button(control_frame, text="فتح ملف الطالب", font=FONTS["small"], bg=COLORS["primary"], fg="white",
               command=open_selected).pack(side="right", padx=10)
     tk.Button(control_frame, text="نقل إلى معلم آخر", font=FONTS["small"], bg=COLORS["warning"], fg="white").pack(
          side="right", padx=10)
     tk.Button(control_frame, text="حذف الطالب", font=FONTS["small"], bg=COLORS["danger"], fg="white").pack(side="right",
                                                                                                            padx=10)

def show_admin_settings(tab):
     tab.pack_propagate(False)

     # تنظيف المحتوى السابق إذا وجد
     for widget in tab.winfo_children():
          widget.destroy()

     # ========== إطار التمرير الرئيسي ==========
     main_frame = tk.Frame(tab)
     main_frame.pack(fill="both", expand=True)

     canvas = tk.Canvas(main_frame)
     scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
     scrollable_frame = tk.Frame(canvas)

     scrollable_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
     canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
     canvas.configure(yscrollcommand=scrollbar.set)

     scrollbar.pack(side="left", fill="y")
     canvas.pack(side="right", fill="both", expand=True)

     # ========== عنوان الصفحة ==========
     tk.Label(scrollable_frame, text="⚙️ إعدادات النظام",
              font=FONTS["title"], fg=COLORS["primary"]).pack(pady=20)

     # ========== إعدادات النظام العامة ==========
     general_frame = tk.Frame(scrollable_frame, bd=1, relief="solid", padx=10, pady=10)
     general_frame.pack(fill="x", padx=20, pady=10)

     tk.Label(general_frame, text="الإعدادات العامة",
              font=FONTS["subtitle"], fg=COLORS["dark"]).grid(row=0, column=0, columnspan=2, pady=5, sticky="w")

     # اسم المؤسسة
     tk.Label(general_frame, text="اسم المؤسسة:",
              font=FONTS["normal"]).grid(row=1, column=0, sticky="e", padx=10, pady=5)
     org_name = tk.Entry(general_frame, font=FONTS["normal"])
     org_name.insert(0, SYSTEM_NAME)
     org_name.grid(row=1, column=1, sticky="ew", padx=10, pady=5)

     # إعدادات الوقت
     tk.Label(general_frame, text="عدد أيام العمل بالأسبوع:",
              font=FONTS["normal"]).grid(row=2, column=0, sticky="e", padx=10, pady=5)
     work_days = ttk.Combobox(general_frame, values=["5 أيام", "6 أيام", "7 أيام"],
                              font=FONTS["normal"], state="readonly")
     work_days.current(0)
     work_days.grid(row=2, column=1, sticky="ew", padx=10, pady=5)

     tk.Label(general_frame, text="بداية اليوم الدراسي:",
              font=FONTS["normal"]).grid(row=3, column=0, sticky="e", padx=10, pady=5)
     start_time = ttk.Combobox(general_frame,
                               values=["7:00 ص", "8:00 ص", "9:00 ص", "10:00 ص"],
                               font=FONTS["normal"], state="readonly")
     start_time.current(1)
     start_time.grid(row=3, column=1, sticky="ew", padx=10, pady=5)

     tk.Label(general_frame, text="نهاية اليوم الدراسي:",
              font=FONTS["normal"]).grid(row=4, column=0, sticky="e", padx=10, pady=5)
     end_time = ttk.Combobox(general_frame,
                             values=["1:00 م", "2:00 م", "3:00 م", "4:00 م", "5:00 م"],
                             font=FONTS["normal"], state="readonly")
     end_time.current(1)
     end_time.grid(row=4, column=1, sticky="ew", padx=10, pady=5)

     # لغة النظام
     tk.Label(general_frame, text="لغة النظام:",
              font=FONTS["normal"]).grid(row=5, column=0, sticky="e", padx=10, pady=5)
     language = ttk.Combobox(general_frame, values=["العربية", "English"],
                             font=FONTS["normal"], state="readonly")
     language.current(0)
     language.grid(row=5, column=1, sticky="ew", padx=10, pady=5)

     # ========== إعدادات النسخ الاحتياطي ==========
     backup_frame = tk.Frame(scrollable_frame, bd=1, relief="solid", padx=10, pady=10)
     backup_frame.pack(fill="x", padx=20, pady=10)

     tk.Label(backup_frame, text="إعدادات النسخ الاحتياطي",
              font=FONTS["subtitle"], fg=COLORS["dark"]).grid(row=0, column=0, columnspan=2, pady=5, sticky="w")

     # النسخ التلقائي
     tk.Label(backup_frame, text="النسخ التلقائي:",
              font=FONTS["normal"]).grid(row=1, column=0, sticky="e", padx=10, pady=5)
     auto_backup = ttk.Combobox(backup_frame,
                                values=["معطل", "يومي", "أسبوعي", "شهري"],
                                font=FONTS["normal"], state="readonly")
     auto_backup.current(0)
     auto_backup.grid(row=1, column=1, sticky="ew", padx=10, pady=5)

     # مسار النسخ الاحتياطي الافتراضي
     tk.Label(backup_frame, text="مسار الحفظ الافتراضي:",
              font=FONTS["normal"]).grid(row=2, column=0, sticky="e", padx=10, pady=5)
     backup_path = tk.Entry(backup_frame, font=FONTS["normal"])
     backup_path.insert(0, "backups/")
     backup_path.grid(row=2, column=1, sticky="ew", padx=10, pady=5)

     def browse_backup_path():
          path = filedialog.askdirectory()
          if path:
               backup_path.delete(0, tk.END)
               backup_path.insert(0, path)

     tk.Button(backup_frame, text="استعراض", font=FONTS["small"],
               command=browse_backup_path).grid(row=2, column=2, padx=5)

     # عدد النسخ المحفوظة
     tk.Label(backup_frame, text="الحد الأقصى للنسخ المحفوظة:",
              font=FONTS["normal"]).grid(row=3, column=0, sticky="e", padx=10, pady=5)
     max_backups = ttk.Combobox(backup_frame,
                                values=["5", "10", "20", "30", "50"],
                                font=FONTS["normal"], state="readonly")
     max_backups.current(1)
     max_backups.grid(row=3, column=1, sticky="ew", padx=10, pady=5)

     # أزرار النسخ الاحتياطي
     btn_frame = tk.Frame(backup_frame)
     btn_frame.grid(row=4, column=0, columnspan=3, pady=10)

     def backup_now():
          backup_file = filedialog.asksaveasfilename(
               defaultextension=".nbk",
               filetypes=[("Noor Albyan Backup", "*.nbk")],
               initialdir=backup_path.get(),
               title="حفظ نسخة احتياطية"
          )
          if backup_file:
               try:
                    save_data()
                    # هنا يمكنك إضافة منطق إنشاء النسخة الاحتياطية
                    messagebox.showinfo("تم", f"تم إنشاء نسخة احتياطية في:\n{backup_file}")
               except Exception as e:
                    messagebox.showerror("خطأ", f"فشل في إنشاء النسخة الاحتياطية:\n{str(e)}")

     def restore_backup():
          backup_file = filedialog.askopenfilename(
               filetypes=[("Noor Albyan Backup", "*.nbk")],
               title="اختر ملف النسخة الاحتياطية"
          )
          if backup_file:
               if messagebox.askyesno("تأكيد",
                                      "هل أنت متأكد من استعادة النسخة الاحتياطية؟\nسيتم استبدال جميع البيانات الحالية"):
                    try:
                         # هنا يمكنك إضافة منطق استعادة النسخة الاحتياطية
                         messagebox.showinfo("تم", f"تم استعادة النسخة الاحتياطية من:\n{backup_file}")
                         load_data()  # إعادة تحميل البيانات بعد الاستعادة
                    except Exception as e:
                         messagebox.showerror("خطأ", f"فشل في استعادة النسخة الاحتياطية:\n{str(e)}")

     tk.Button(btn_frame, text="إنشاء نسخة احتياطية الآن", font=FONTS["normal"],
               bg=COLORS["primary"], fg="white", command=backup_now).pack(side="right", padx=10)
     tk.Button(btn_frame, text="استعادة نسخة احتياطية", font=FONTS["normal"],
               bg=COLORS["warning"], fg="white", command=restore_backup).pack(side="right", padx=10)

     # ========== إعدادات الأمان ==========
     security_frame = tk.Frame(scrollable_frame, bd=1, relief="solid", padx=10, pady=10)
     security_frame.pack(fill="x", padx=20, pady=10)

     tk.Label(security_frame, text="إعدادات الأمان",
              font=FONTS["subtitle"], fg=COLORS["dark"]).grid(row=0, column=0, columnspan=2, pady=5, sticky="w")

     # سياسة كلمات المرور
     tk.Label(security_frame, text="قوة كلمة المرور:",
              font=FONTS["normal"]).grid(row=1, column=0, sticky="e", padx=10, pady=5)
     password_policy = ttk.Combobox(security_frame,
                                    values=["ضعيفة (4 أحرف)", "متوسطة (6 أحرف)", "قوية (8 أحرف مع رموز)"],
                                    font=FONTS["normal"], state="readonly")
     password_policy.current(1)
     password_policy.grid(row=1, column=1, sticky="ew", padx=10, pady=5)

     # مدة انتهاء الجلسة
     tk.Label(security_frame, text="مدة انتهاء الجلسة (دقيقة):",
              font=FONTS["normal"]).grid(row=2, column=0, sticky="e", padx=10, pady=5)
     session_timeout = ttk.Combobox(security_frame,
                                    values=["15", "30", "60", "120", "لا تنتهي"],
                                    font=FONTS["normal"], state="readonly")
     session_timeout.current(2)
     session_timeout.grid(row=2, column=1, sticky="ew", padx=10, pady=5)

     # تسجيل الدخول المتعدد
     tk.Label(security_frame, text="تسجيل الدخول المتعدد:",
              font=FONTS["normal"]).grid(row=3, column=0, sticky="e", padx=10, pady=5)
     multi_login = ttk.Combobox(security_frame,
                                values=["مسموح", "غير مسموح"],
                                font=FONTS["normal"], state="readonly")
     multi_login.current(0)
     multi_login.grid(row=3, column=1, sticky="ew", padx=10, pady=5)

     # ========== معلومات النظام ==========
     info_frame = tk.Frame(scrollable_frame, bd=1, relief="solid", padx=10, pady=10)
     info_frame.pack(fill="x", padx=20, pady=10)

     tk.Label(info_frame, text="معلومات النظام",
              font=FONTS["subtitle"], fg=COLORS["dark"]).grid(row=0, column=0, columnspan=2, pady=5, sticky="w")

     # معلومات النظام
     info_text = f"""
    إصدار النظام: {VERSION}
    تاريخ الإصدار: 2025-07-01
    المطور: {DEVELOPER}
    البريد الإلكتروني: {CONTACT_EMAIL}
    الموقع الإلكتروني: {WEBSITE}

    مساحة التخزين المستخدمة: 25.4 MB
    عدد المستخدمين: {len(USERS)}
    عدد المراكز: {len(CENTERS)}
    عدد الطلاب: {len(get_all_students())}

    © 2025 جميع الحقوق محفوظة
    """
     tk.Label(info_frame, text=info_text,
              font=FONTS["small"], justify="right").grid(row=1, column=0, columnspan=2, sticky="e", pady=5)

     # أزرار معلومات النظام
     btn_frame = tk.Frame(info_frame)
     btn_frame.grid(row=2, column=0, columnspan=2, pady=10)

     def visit_website():
          webbrowser.open(f"https://{WEBSITE}")

     def check_updates():
          messagebox.showinfo("التحديثات", "جارٍ التحقق من وجود تحديثات...\nالإصدار الحالي هو الأحدث")

     def show_license():
          license_text = """
        ترخيص استخدام نظام نور البيان

        1. هذا البرنامج مقدم للاستخدام الداخلي فقط.
        2. لا يسمح بتوزيع أو تعديل البرنامج دون إذن.
        3. جميع الحقوق محفوظة لفريق نور البيان.

        للاستفسارات: {CONTACT_EMAIL}
        """
          messagebox.showinfo("اتفاقية الترخيص", license_text)

     tk.Button(btn_frame, text="زيارة موقعنا", font=FONTS["small"],
               bg=COLORS["info"], fg="white", command=visit_website).pack(side="right", padx=5)
     tk.Button(btn_frame, text="التحقق من التحديثات", font=FONTS["small"],
               bg=COLORS["secondary"], fg="white", command=check_updates).pack(side="right", padx=5)
     tk.Button(btn_frame, text="عرض الرخصة", font=FONTS["small"],
               bg=COLORS["dark"], fg="white", command=show_license).pack(side="right", padx=5)

     # ========== زر حفظ الإعدادات ==========
     def save_settings():
          # هنا يمكنك إضافة منطق حفظ الإعدادات
          messagebox.showinfo("تم", "تم حفظ الإعدادات بنجاح")

          # يمكنك تحديث متغيرات النظام مثل:
          # global SYSTEM_NAME
          # SYSTEM_NAME = org_name.get()
          # ثم حفظها في ملف إعدادات

     save_btn = tk.Button(scrollable_frame, text="حفظ جميع الإعدادات",
                          font=FONTS["normal"], bg=COLORS["success"],
                          fg="white", command=save_settings)
     save_btn.pack(pady=20)

# ----- لوحة إدارة المركز مع تحسينات -----
def center_dashboard(username):
     # الحصول على بيانات المستخدم والمركز
     user = next((u for u in USERS if u["username"] == username), None)
     if not user:
          messagebox.showerror("خطأ", "لم يتم العثور على بيانات المستخدم")
          return

     center_name = user.get("center", "")
     if not center_name:
          messagebox.showerror("خطأ", "لم يتم تعيين مركز لهذا المستخدم")
          return

     # إنشاء النافذة الرئيسية
     root = tk.Tk()
     root.title(f"لوحة إدارة المركز - {SYSTEM_NAME} | {center_name}")
     root.geometry("1300x850")  # زيادة حجم النافذة لاستيعاب المزيد من المحتوى
     center_window(root, 1300, 850)

     # تحميل أيقونة البرنامج إذا وجدت
     try:
          root.iconbitmap("assets/icon.ico")  # مسار أيقونة البرنامج
     except:
          pass

     # شريط الأدوات العلوي مع تحسينات
     toolbar = tk.Frame(root, bg=COLORS["primary"], height=60)
     toolbar.pack(fill="x")

     # إضافة شعار المركز إذا وجد
     try:
          logo_img = Image.open(f"centers/{center_name}/logo.png").resize((40, 40))
          logo_photo = ImageTk.PhotoImage(logo_img)
          logo_label = tk.Label(toolbar, image=logo_photo, bg=COLORS["primary"])
          logo_label.image = logo_photo
          logo_label.pack(side="right", padx=10)
     except:
          pass

     # معلومات المستخدم مع صورة البروفايل
     user_frame = tk.Frame(toolbar, bg=COLORS["primary"])
     user_frame.pack(side="right", padx=20)

     try:
          profile_img = Image.open(f"users/{username}/profile.png").resize((30, 30))
          profile_photo = ImageTk.PhotoImage(profile_img)
          profile_label = tk.Label(user_frame, image=profile_photo, bg=COLORS["primary"])
          profile_label.image = profile_photo
          profile_label.pack(side="right")
     except:
          pass

     tk.Label(user_frame, text=f"مرحباً بك، {user.get('full_name', username)}",
              font=FONTS["subtitle"], bg=COLORS["primary"], fg="white").pack(side="right", padx=10)

     # أزرار التحكم في الشريط العلوي
     btn_frame = tk.Frame(toolbar, bg=COLORS["primary"])
     btn_frame.pack(side="left", padx=10)

     def logout():
          if messagebox.askyesno("تأكيد", "هل أنت متأكد من تسجيل الخروج؟"):
               root.destroy()
               login_screen()

     def backup_data():
          try:
               backup_file = filedialog.asksaveasfilename(
                    defaultextension=".nbk",
                    filetypes=[("نسخة احتياطية", "*.nbk")],
                    title="حفظ نسخة احتياطية"
               )
               if backup_file:
                    save_data()
                    shutil.make_archive(backup_file.replace('.nbk', ''), 'zip', 'data')
                    messagebox.showinfo("نجاح", "تم إنشاء النسخة الاحتياطية بنجاح")
          except Exception as e:
               messagebox.showerror("خطأ", f"فشل في إنشاء النسخة الاحتياطية: {str(e)}")

     def show_notifications():
          # دالة لعرض الإشعارات والتنبيهات
          notifications = [
               f"لديك {len([s for s in get_all_students_in_center(center_name) if s.get('fees_status') == 'غير مدفوع'])} طلاب متأخرين في السداد",
               f"لديك {len([t for t in get_teachers_in_center(center_name) if not t.get('active', True)])} معلمين غير نشطين"
          ]

          notification_window = tk.Toplevel(root)
          notification_window.title("الإشعارات والتنبيهات")
          notification_window.geometry("400x300")
          center_window(notification_window, 400, 300)

          tk.Label(notification_window, text="الإشعارات الحديثة",
                   font=FONTS["subtitle"], fg=COLORS["primary"]).pack(pady=10)

          for note in notifications:
               tk.Label(notification_window, text=f"• {note}",
                        font=FONTS["small"], wraplength=380, justify="right").pack(pady=5, padx=10, anchor="w")

     # أزرار الشريط العلوي
     tk.Button(btn_frame, text="تسجيل الخروج", font=FONTS["small"], bg=COLORS["danger"],
               fg="white", command=logout).pack(side="left", padx=5)
     tk.Button(btn_frame, text="حفظ البيانات", font=FONTS["small"], bg=COLORS["success"],
               fg="white", command=save_data).pack(side="left", padx=5)
     tk.Button(btn_frame, text="نسخة احتياطية", font=FONTS["small"], bg=COLORS["warning"],
               fg="white", command=backup_data).pack(side="left", padx=5)
     tk.Button(btn_frame, text="الإشعارات", font=FONTS["small"], bg=COLORS["info"],
               fg="white", command=show_notifications).pack(side="left", padx=5)

     # تبويبات التحكم مع تحسينات
     style = ttk.Style()
     style.configure("Center.TNotebook", tabposition="n", padding=[20, 5])
     style.configure("Center.TNotebook.Tab",
                     font=FONTS["subtitle"],
                     padding=[15, 10],
                     background=COLORS["light"],
                     foreground=COLORS["dark"])

     nb = ttk.Notebook(root, style="Center.TNotebook")
     nb.pack(fill="both", expand=True, padx=10, pady=5)

     # إنشاء التبويبات مع أيقونات
     tabs = {
          "الرئيسية": {"frame": tk.Frame(nb, bg="#f8f9fa"), "icon": "🏠"},
          "المعلمين": {"frame": tk.Frame(nb), "icon": "👨‍🏫"},
          "الطلاب": {"frame": tk.Frame(nb), "icon": "🧑‍🎓"},
          "الرسوم": {"frame": tk.Frame(nb), "icon": "💵"},
          "الرواتب": {"frame": tk.Frame(nb), "icon": "💰"},
          "التقارير": {"frame": tk.Frame(nb), "icon": "📊"},
          "الإعدادات": {"frame": tk.Frame(nb), "icon": "⚙️"}
     }

     for name, tab_data in tabs.items():
          nb.add(tab_data["frame"], text=f"{tab_data['icon']} {name}")

     # تعبئة التبويبات بالمحتوى مع معالجة الأخطاء
     try:
          show_center_main(tabs["الرئيسية"]["frame"], center_name)
          show_center_teachers_tab(tabs["المعلمين"]["frame"], center_name)
          show_center_students_tab(tabs["الطلاب"]["frame"], center_name)
          show_center_fees_tab(tabs["الرسوم"]["frame"], center_name)
          show_center_salaries_tab(tabs["الرواتب"]["frame"], center_name)
          show_center_reports_tab(tabs["التقارير"]["frame"], center_name)

          # تبويب الإعدادات الجديد
          settings_frame = tabs["الإعدادات"]["frame"]
          settings_frame.pack_propagate(False)

          tk.Label(settings_frame, text="إعدادات المركز",
                   font=FONTS["title"], fg=COLORS["primary"]).pack(pady=20)

          # إعدادات عامة
          settings_form = tk.Frame(settings_frame, padx=20, pady=10)
          settings_form.pack(fill="x")

          tk.Label(settings_form, text="اسم المركز:", font=FONTS["normal"]).grid(row=0, column=1, sticky="e", pady=5)
          center_name_entry = ttk.Entry(settings_form, font=FONTS["normal"])
          center_name_entry.insert(0, center_name)
          center_name_entry.grid(row=0, column=0, sticky="ew", pady=5)

          tk.Label(settings_form, text="ساعات العمل:", font=FONTS["normal"]).grid(row=1, column=1, sticky="e", pady=5)
          work_hours_entry = ttk.Entry(settings_form, font=FONTS["normal"])
          work_hours_entry.insert(0, "08:00 ص - 04:00 م")
          work_hours_entry.grid(row=1, column=0, sticky="ew", pady=5)

          # زر حفظ الإعدادات
          def save_settings():
               new_name = center_name_entry.get()
               if new_name != center_name:
                    if messagebox.askyesno("تأكيد", "تغيير اسم المركز سيؤثر على جميع البيانات. هل تريد المتابعة؟"):
                         # تحديث اسم المركز في جميع السجلات
                         pass
               messagebox.showinfo("تم", "تم حفظ الإعدادات بنجاح")

          tk.Button(settings_frame, text="حفظ الإعدادات", font=FONTS["normal"],
                    bg=COLORS["success"], fg="white", command=save_settings).pack(pady=20)

     except Exception as e:
          messagebox.showerror("خطأ", f"حدث خطأ أثناء تحميل البيانات: {str(e)}")
          root.destroy()
          return

     # شريط الحالة السفلي مع تحسينات
     status_bar = tk.Frame(root, bg=COLORS["dark"], height=30)
     status_bar.pack(fill="x", side="bottom")

     status_labels = tk.Frame(status_bar, bg=COLORS["dark"])
     status_labels.pack(side="right", padx=10)

     # عرض إحصائيات سريعة
     students_count = len(get_all_students_in_center(center_name))
     teachers_count = len(get_teachers_in_center(center_name))
     unpaid_fees = len([s for s in get_all_students_in_center(center_name) if s.get('fees_status') == 'غير مدفوع'])

     tk.Label(status_labels,
              text=f"الطلاب: {students_count} | المعلمون: {teachers_count} | متأخرون في السداد: {unpaid_fees}",
              font=FONTS["small"], bg=COLORS["dark"], fg="white").pack(side="right", padx=5)

     tk.Label(status_labels,
              text=f"{SYSTEM_NAME} | الإصدار {VERSION} | {get_today_date()} | {get_current_time()}",
              font=FONTS["small"], bg=COLORS["dark"], fg="white").pack(side="right", padx=5)

     # تحديث تلقائي للبيانات كل 5 دقائق
     def auto_refresh():
          try:
               load_data()  # إعادة تحميل البيانات من الملفات
               # يمكن هنا تحديث عناصر واجهة المستخدم حسب الحاجة
               root.after(300000, auto_refresh)  # 5 دقائق = 300000 مللي ثانية
          except:
               pass

     root.after(300000, auto_refresh)

     root.mainloop()

def show_center_main(tab, center_name):
     tab.pack_propagate(False)

     # تنظيف المحتوى السابق إذا وجد
     for widget in tab.winfo_children():
          widget.destroy()

     # ========== إطار العنوان مع تحسينات ==========
     header = tk.Frame(tab, bg=COLORS["primary"], height=80)
     header.pack(fill="x", pady=(0, 10))

     # إضافة أيقونة المركز إذا وجدت
     try:
          logo_img = Image.open(f"centers/{center_name}/logo.png").resize((50, 50))
          logo_photo = ImageTk.PhotoImage(logo_img)
          logo_label = tk.Label(header, image=logo_photo, bg=COLORS["primary"])
          logo_label.image = logo_photo
          logo_label.pack(side="right", padx=10)
     except:
          pass

     tk.Label(header, text=f"نظرة عامة على مركز {center_name}",
              font=FONTS["title"], bg=COLORS["primary"], fg="white").pack(pady=20)

     # زر تحديث البيانات
     def refresh_data():
          load_data()
          show_center_main(tab, center_name)

     refresh_btn = tk.Button(header, text="تحديث البيانات", font=FONTS["small"],
                             bg=COLORS["accent"], fg="white", command=refresh_data)
     refresh_btn.pack(side="left", padx=20)

     # ========== معلومات المركز مع تحسينات ==========
     center = next((c for c in CENTERS if c["name"] == center_name), None)
     if not center:
          messagebox.showerror("خطأ", "لم يتم العثور على بيانات المركز")
          return

     info_frame = tk.Frame(tab, bg="white", bd=1, relief="solid", padx=10, pady=10)
     info_frame.pack(fill="x", padx=20, pady=(0, 20))

     # إنشاء شبكة لعرض المعلومات بطريقة منظمة
     info_grid = tk.Frame(info_frame, bg="white")
     info_grid.pack(fill="x")

     details = [
          ("عنوان المركز:", center.get("address", "غير محدد")),
          ("هاتف المركز:", center.get("phone", "غير محدد")),
          ("البريد الإلكتروني:", center.get("email", "غير محدد")),
          ("مدير المركز:", center.get("manager", "غير محدد")),
          ("تاريخ الإنشاء:", center.get("created_date", "غير محدد")),
          ("حالة المركز:", center.get("status", "نشط"))
     ]

     for i, (label, value) in enumerate(details):
          row = i % 3  # 3 أعمدة
          col = i // 3

          tk.Label(info_grid, text=label, font=FONTS["normal"], bg="white",
                   fg=COLORS["dark"], anchor="e").grid(row=row, column=col * 2, padx=5, pady=5, sticky="e")
          tk.Label(info_grid, text=value, font=FONTS["normal"], bg="white",
                   fg=COLORS["primary"], anchor="w").grid(row=row, column=col * 2 + 1, padx=5, pady=5, sticky="w")

     # ========== إطار الإحصائيات مع تحسينات ==========
     stats_container = tk.Frame(tab, bg="#f0f0f0", padx=10, pady=10)
     stats_container.pack(fill="x", pady=(0, 20))

     teachers = get_teachers_in_center(center_name)
     students = get_all_students_in_center(center_name)

     # حساب إحصائيات الحضور الفعلية
     attendance_records = []
     for teacher in TEACHERS:
          if teacher.get("center") == center_name:
               for student in teacher.get("students", []):
                    attendance_records.extend(student.get("attendance", []))

     today_attendance = [a for a in attendance_records if a.get("date") == get_today_date()]
     attendance_percentage = "0%"
     if today_attendance:
          present = sum(1 for a in today_attendance if a.get("status") == "حاضر")
          attendance_percentage = f"{round((present / len(today_attendance)) * 100)}%"

     stats = [
          {"label": "عدد المعلمين", "value": len(teachers), "color": COLORS["primary"], "icon": "👨‍🏫"},
          {"label": "عدد الطلاب", "value": len(students), "color": COLORS["secondary"], "icon": "🧑‍🎓"},
          {"label": "تسميعات اليوم", "value": sum(len([r for r in s.get("recitations", [])
                                                       if r.get("date") == get_today_date()])
                                                  for s in students),
           "color": COLORS["info"], "icon": "📖"},
          {"label": "الحضور اليوم", "value": attendance_percentage, "color": COLORS["success"], "icon": "✅"},
          {"label": "طلاب جدد", "value": sum(1 for s in students
                                             if
                                             s.get("join_date", "").startswith(datetime.datetime.now().strftime("%Y-%m"))),
           "color": COLORS["warning"], "icon": "🆕"},
          {"label": "متوسط التسميع", "value": round(len([r for s in students
                                                         for r in s.get("recitations", [])]) / max(1, len(students))),
           "color": COLORS["accent"], "icon": "📊"}
     ]

     for i, stat in enumerate(stats):
          stat_frame = tk.Frame(stats_container, bg=stat["color"], bd=2, relief="ridge",
                                width=200, height=120)
          stat_frame.grid(row=i // 3, column=i % 3, padx=10, pady=10, sticky="nsew")
          stat_frame.grid_propagate(False)

          # إضافة أيقونة
          tk.Label(stat_frame, text=stat["icon"], font=("Arial", 20),
                   bg=stat["color"], fg="white").pack(pady=(10, 0))

          # إضافة التسمية
          tk.Label(stat_frame, text=stat["label"], font=FONTS["small"],
                   bg=stat["color"], fg="white").pack()

          # إضافة القيمة
          tk.Label(stat_frame, text=str(stat["value"]), font=("Cairo", 24, "bold"),
                   bg=stat["color"], fg="white").pack(pady=(5, 10))

          # جعل الإحصائيات قابلة للنقر لعرض التفاصيل
          def show_stat_details(stat_data=stat):
               detail_window = tk.Toplevel()
               detail_window.title(f"تفاصيل {stat_data['label']}")
               detail_window.geometry("500x400")
               center_window(detail_window, 500, 400)

               tk.Label(detail_window, text=f"تفاصيل {stat_data['label']}",
                        font=FONTS["subtitle"], fg=stat_data["color"]).pack(pady=10)

               if stat_data["label"] == "عدد المعلمين":
                    cols = ("اسم المعلم", "عدد الطلاب", "آخر دخول")
                    tree = ttk.Treeview(detail_window, columns=cols, show="headings")

                    for col in cols:
                         tree.heading(col, text=col)
                         tree.column(col, width=120, anchor="center")

                    for teacher in teachers:
                         user = next((u for u in USERS if u["username"] == teacher["username"]), None)
                         last_login = user.get("last_login", "لم يسجل دخول") if user else "غير معروف"

                         tree.insert("", "end", values=(
                              teacher.get("full_name", teacher["username"]),
                              len(teacher.get("students", [])),
                              last_login
                         ))

                    tree.pack(fill="both", expand=True, padx=10, pady=10)

               elif stat_data["label"] == "عدد الطلاب":
                    cols = ("اسم الطالب", "المعلم", "المستوى", "تاريخ الانضمام")
                    tree = ttk.Treeview(detail_window, columns=cols, show="headings")

                    for col in cols:
                         tree.heading(col, text=col)
                         tree.column(col, width=120, anchor="center")

                    for student in students[:50]:  # عرض أول 50 طالب فقط
                         tree.insert("", "end", values=(
                              student["name"],
                              student["teacher"],
                              student["level"],
                              student.get("join_date", "")
                         ))

                    tree.pack(fill="both", expand=True, padx=10, pady=10)

               # يمكن إضافة حالات أخرى لعرض تفاصيل الإحصائيات الأخرى

          stat_frame.bind("<Button-1>", lambda e, s=stat: show_stat_details(s))
          for child in stat_frame.winfo_children():
               child.bind("<Button-1>", lambda e, s=stat: show_stat_details(s))

     # ========== أحدث الأنشطة مع تحسينات ==========
     activities_container = tk.Frame(tab, bg="white", bd=1, relief="solid")
     activities_container.pack(fill="both", expand=True, padx=20, pady=(0, 20))

     # شريط عنوان الأنشطة
     activities_header = tk.Frame(activities_container, bg=COLORS["light"])
     activities_header.pack(fill="x", pady=(0, 10))

     tk.Label(activities_header, text="أحدث الأنشطة",
              font=FONTS["subtitle"], bg=COLORS["light"]).pack(side="right", padx=10)

     # زر لعرض المزيد من الأنشطة
     def show_all_activities():
          all_activities_window = tk.Toplevel()
          all_activities_window.title("سجل الأنشطة الكامل")
          all_activities_window.geometry("800x600")
          center_window(all_activities_window, 800, 600)

          # يمكن هنا جلب سجل الأنشطة الكامل من قاعدة البيانات
          tk.Label(all_activities_window, text="سجل الأنشطة الكامل للمركز",
                   font=FONTS["title"], fg=COLORS["primary"]).pack(pady=10)

          # مثال بسيط - في الواقع يجب جلب البيانات من السجلات
          tk.Label(all_activities_window, text="سيتم عرض سجل الأنشطة الكامل هنا",
                   font=FONTS["normal"]).pack(pady=50)

     tk.Button(activities_header, text="عرض الكل", font=FONTS["small"],
               bg=COLORS["primary"], fg="white", command=show_all_activities).pack(side="left", padx=10)

     # إطار التمرير للأنشطة
     canvas = tk.Canvas(activities_container, bg="white", highlightthickness=0)
     scrollbar = ttk.Scrollbar(activities_container, orient="vertical", command=canvas.yview)
     scrollable_frame = tk.Frame(canvas, bg="white")

     scrollable_frame.bind(
          "<Configure>",
          lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
     )

     canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
     canvas.configure(yscrollcommand=scrollbar.set)

     scrollbar.pack(side="left", fill="y")
     canvas.pack(side="right", fill="both", expand=True)

     # جلب الأنشطة الفعلية من البيانات
     activities = []

     # أنشطة تسجيل الدخول
     for user in USERS:
          if user.get("last_login") and user.get("center") == center_name:
               activities.append((f"تم تسجيل دخول {user['role']} '{user['username']}'",
                                  user["last_login"]))

     # أنشطة التسميع
     for teacher in TEACHERS:
          if teacher.get("center") == center_name:
               for student in teacher.get("students", []):
                    for recitation in student.get("recitations", []):
                         if recitation.get("date") == get_today_date():
                              activities.append((f"تم تسجيل تسميع للطالب {student['name']} - {recitation['surah']}",
                                                 f"{recitation.get('time', '')} {recitation['date']}"))

     # ترتيب الأنشطة حسب الوقت (الأحدث أولاً)
     activities.sort(key=lambda x: x[1], reverse=True)
     activities = activities[:10]  # عرض آخر 10 أنشطة فقط

     for activity, time in activities:
          activity_frame = tk.Frame(scrollable_frame, bg="white")
          activity_frame.pack(fill="x", padx=10, pady=5)

          # أيقونة النشاط
          tk.Label(activity_frame, text="•", font=FONTS["normal"],
                   bg="white", fg=COLORS["secondary"]).pack(side="right", padx=5)

          # تفاصيل النشاط
          tk.Label(activity_frame, text=activity, font=FONTS["small"],
                   bg="white", wraplength=400, justify="right").pack(side="right", padx=5)

          # وقت النشاط
          tk.Label(activity_frame, text=time, font=FONTS["small"],
                   bg="white", fg=COLORS["dark"]).pack(side="left", padx=10)

     # ========== مخططات بيانية (إضافة جديدة) ==========
     charts_frame = tk.Frame(tab, bg="white", bd=1, relief="solid")
     charts_frame.pack(fill="x", padx=20, pady=(0, 20))

     tk.Label(charts_frame, text="مخططات أداء المركز",
              font=FONTS["subtitle"], bg="white").pack(pady=10)

     # رسم بياني مبسط لتوزيع الطلاب حسب المستوى
     canvas = tk.Canvas(charts_frame, width=800, height=200, bg="white", highlightthickness=0)
     canvas.pack(pady=10)

     # حساب توزيع الطلاب حسب المستوى
     levels = {}
     for student in students:
          level = student.get("level", "غير محدد")
          levels[level] = levels.get(level, 0) + 1

     if levels:
          max_count = max(levels.values())
          bar_height = lambda val: (val / max_count) * 180 if max_count > 0 else 0

          colors = [COLORS["primary"], COLORS["secondary"], COLORS["info"],
                    COLORS["success"], COLORS["warning"], COLORS["accent"]]

          for i, (level, count) in enumerate(levels.items()):
               x1 = 100 + i * 120
               x2 = 200 + i * 120
               canvas.create_rectangle(x1, 200 - bar_height(count), x2, 200,
                                       fill=colors[i % len(colors)], outline="")
               canvas.create_text(x1 + 50, 180 - bar_height(count),
                                  text=str(count), font=FONTS["small"])
               canvas.create_text(x1 + 50, 220, text=level, font=FONTS["small"])

def show_center_teachers_tab(tab, center_name):
     tab.pack_propagate(False)

     try:
          # تنظيف المحتوى السابق إذا وجد
          for widget in tab.winfo_children():
               widget.destroy()

          if not TEACHERS:
               tk.Label(tab, text="لا توجد بيانات للمعلمين متاحة",
                        font=FONTS["subtitle"], fg=COLORS["danger"]).pack(pady=50)
               return
     except Exception as e:
          error_msg = f"حدث خطأ أثناء تحميل بيانات المعلمين: {str(e)}"
          messagebox.showerror("خطأ", error_msg)

          # إنشاء واجهة بديلة لعرض الخطأ
          for widget in tab.winfo_children():
               widget.destroy()

          error_frame = tk.Frame(tab)
          error_frame.pack(expand=True, fill="both", padx=50, pady=50)

          tk.Label(error_frame, text="حدث خطأ في تحميل البيانات",
                   font=FONTS["title"], fg=COLORS["danger"]).pack()

          tk.Label(error_frame, text=error_msg,
                   font=FONTS["normal"], wraplength=400).pack()

          tk.Button(error_frame, text="إعادة المحاولة", command=lambda: show_center_teachers_tab(tab, center_name),
                    bg=COLORS["primary"], fg="white").pack(pady=20)

     # ========== شريط الأدوات مع تحسينات ==========
     toolbar = tk.Frame(tab, bg=COLORS["light"], pady=5)
     toolbar.pack(fill="x")

     # العنوان مع أيقونة
     title_frame = tk.Frame(toolbar, bg=COLORS["light"])
     title_frame.pack(side="right", padx=10)
     tk.Label(title_frame, text=f"👨‍🏫 إدارة المعلمين - {center_name}",
              font=FONTS["subtitle"], bg=COLORS["light"]).pack(side="right")

     # مجموعة أزرار التحكم
     btn_frame = tk.Frame(toolbar, bg=COLORS["light"])
     btn_frame.pack(side="left", padx=10)

     def refresh():
          load_data()  # إعادة تحميل البيانات أولاً
          show_center_teachers_tab(tab, center_name)

     def add_new_teacher():
          dialog = tk.Toplevel()
          dialog.title("إضافة معلم جديد")
          dialog.geometry("700x800")
          center_window(dialog, 700, 800)

          # إطار التمرير للنموذج
          main_frame = tk.Frame(dialog)
          main_frame.pack(fill="both", expand=True)

          canvas = tk.Canvas(main_frame)
          scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
          scrollable_frame = tk.Frame(canvas)

          scrollable_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
          canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
          canvas.configure(yscrollcommand=scrollbar.set)

          scrollbar.pack(side="left", fill="y")
          canvas.pack(side="right", fill="both", expand=True)

          tk.Label(scrollable_frame, text="إضافة معلم جديد",
                   font=FONTS["subtitle"], fg=COLORS["primary"]).pack(pady=10)

          # إنشاء حقول النموذج مع حقول إضافية
          fields = [
               {"label": "رقم المعلم", "value": f"TEA{len(TEACHERS) + 1:03d}", "type": "entry", "editable": False},
               {"label": "اسم المستخدم", "value": "", "type": "entry", "required": True},
               {"label": "كلمة المرور", "value": "", "type": "entry", "show": "*", "required": True},
               {"label": "تأكيد كلمة المرور", "value": "", "type": "entry", "show": "*", "required": True},
               {"label": "الاسم الكامل", "value": "", "type": "entry", "required": True},
               {"label": "تاريخ الميلاد", "value": "", "type": "date"},
               {"label": "رقم الهاتف", "value": "", "type": "entry", "validation": "phone"},
               {"label": "البريد الإلكتروني", "value": "", "type": "entry", "validation": "email"},
               {"label": "العنوان", "value": "", "type": "entry"},
               {"label": "الحالة الاجتماعية", "value": "", "type": "combobox",
                "options": ["أعزب", "متزوج", "مطلق", "أرمل"]},
               {"label": "المؤهل العلمي", "value": "", "type": "entry"},
               {"label": "تاريخ التعيين", "value": get_today_date(), "type": "date"},
               {"label": "الراتب الأساسي", "value": "", "type": "entry", "validation": "number"}
          ]

          entries = []
          for field in fields:
               frame = tk.Frame(scrollable_frame)
               frame.pack(fill="x", padx=20, pady=5)

               tk.Label(frame, text=field["label"] + ":", font=FONTS["small"],
                        width=20, anchor="e").pack(side="right")

               if field["type"] == "combobox":
                    var = tk.StringVar(value=field["value"])
                    combo = ttk.Combobox(frame, textvariable=var,
                                         values=field["options"],
                                         font=FONTS["small"], state="readonly")
                    combo.pack(fill="x")
                    entries.append(var)
               elif field["type"] == "date":
                    # يمكن استبدال هذا بمكون اختيار تاريخ متقدم
                    entry = ttk.Entry(frame, font=FONTS["small"])
                    entry.insert(0, field["value"])
                    entry.pack(fill="x")
                    entries.append(entry)
               else:
                    show = field.get("show", None)
                    entry = ttk.Entry(frame, font=FONTS["small"], show=show)
                    entry.insert(0, field["value"])
                    if field.get("editable", True) == False:
                         entry.config(state="readonly")
                    entry.pack(fill="x")
                    entries.append(entry)

          def save_teacher():
               # التحقق من الحقول المطلوبة
               required_fields = [0, 1, 2, 3, 4]  # حقول مطلوبة
               for i in required_fields:
                    if not entries[i].get():
                         messagebox.showerror("خطأ", "جميع الحقول المطلوبة يجب تعبئتها")
                         return

               # التحقق من تطابق كلمتي المرور
               if entries[2].get() != entries[3].get():
                    messagebox.showerror("خطأ", "كلمتا المرور غير متطابقتين")
                    return

               # التحقق من عدم تكرار اسم المستخدم
               if any(u.get('username') == entries[1].get() for u in USERS):
                    messagebox.showerror("خطأ", "اسم المستخدم موجود مسبقاً")
                    return

               # التحقق من البريد الإلكتروني إذا تم إدخاله
               if entries[7].get() and "@" not in entries[7].get():
                    messagebox.showerror("خطأ", "البريد الإلكتروني غير صالح")
                    return

               new_teacher = {
                    "teacher_id": entries[0].get(),
                    "username": entries[1].get(),
                    "full_name": entries[4].get(),
                    "birth_date": entries[5].get(),
                    "phone": entries[6].get(),
                    "email": entries[7].get(),
                    "address": entries[8].get(),
                    "marital_status": entries[9].get(),
                    "qualification": entries[10].get(),
                    "hire_date": entries[11].get(),
                    "base_salary": entries[12].get(),
                    "center": center_name,
                    "students": [],
                    "status": "نشط"
               }

               new_user = {
                    "user_id": entries[0].get(),
                    "username": entries[1].get(),
                    "password": entries[2].get(),
                    "role": "هيئة التدريس",
                    "full_name": entries[4].get(),
                    "birth_date": entries[5].get(),
                    "phone": entries[6].get(),
                    "email": entries[7].get(),
                    "address": entries[8].get(),
                    "marital_status": entries[9].get(),
                    "center": center_name,
                    "last_login": None,
                    "active": True
               }

               TEACHERS.append(new_teacher)
               USERS.append(new_user)
               save_data()

               dialog.destroy()
               messagebox.showinfo("تم", "تم إضافة المعلم بنجاح")
               refresh()

          btn_frame = tk.Frame(scrollable_frame)
          btn_frame.pack(fill="x", pady=20)

          tk.Button(btn_frame, text="حفظ", font=FONTS["normal"], bg=COLORS["success"],
                    fg="white", command=save_teacher).pack(side="right", padx=10, ipadx=20)
          tk.Button(btn_frame, text="إلغاء", font=FONTS["normal"], bg=COLORS["danger"],
                    fg="white", command=dialog.destroy).pack(side="left", padx=10, ipadx=20)

     # أزرار الشريط العلوي
     tk.Button(btn_frame, text="إضافة معلم", font=FONTS["small"], bg=COLORS["success"],
               fg="white", command=add_new_teacher).pack(side="left", padx=5)
     tk.Button(btn_frame, text="تحديث", font=FONTS["small"], bg=COLORS["info"],
               fg="white", command=refresh).pack(side="left", padx=5)

     # زر تصدير البيانات
     def export_data():
          file_path = filedialog.asksaveasfilename(
               defaultextension=".xlsx",
               filetypes=[("Excel Files", "*.xlsx"), ("CSV Files", "*.csv")],
               title="حفظ بيانات المعلمين"
          )
          if file_path:
               # هنا سيتم تنفيذ عملية التصدير
               messagebox.showinfo("تم", f"تم تصدير بيانات المعلمين إلى {file_path}")

     tk.Button(btn_frame, text="تصدير البيانات", font=FONTS["small"], bg=COLORS["primary"],
               fg="white", command=export_data).pack(side="left", padx=5)

     # ========== إطار البحث والتصفية ==========
     filter_frame = tk.Frame(tab, bg="white", padx=10, pady=10)
     filter_frame.pack(fill="x")

     tk.Label(filter_frame, text="بحث:", font=FONTS["small"], bg="white").pack(side="right")
     search_var = tk.StringVar()
     search_entry = tk.Entry(filter_frame, textvariable=search_var, font=FONTS["small"], width=30)
     search_entry.pack(side="right", padx=5)

     tk.Label(filter_frame, text="تصفية حسب الحالة:", font=FONTS["small"], bg="white").pack(side="right", padx=10)
     status_var = tk.StringVar(value="الكل")
     status_combo = ttk.Combobox(filter_frame, textvariable=status_var,
                                 values=["الكل", "نشط", "غير نشط"],
                                 font=FONTS["small"], state="readonly", width=10)
     status_combo.pack(side="right")

     # ========== جدول المعلمين مع تحسينات ==========
     tree_container = tk.Frame(tab)
     tree_container.pack(fill="both", expand=True, padx=10, pady=(0, 10))

     cols = ("رقم المعلم", "الاسم", "الهاتف", "البريد", "عدد الطلاب", "الحالة", "آخر دخول")
     tree = ttk.Treeview(tree_container, columns=cols, show="headings", style="Custom.Treeview", height=15)

     # تحديد عرض الأعمدة
     col_widths = {
          "رقم المعلم": 100,
          "الاسم": 150,
          "الهاتف": 120,
          "البريد": 150,
          "عدد الطلاب": 80,
          "الحالة": 80,
          "آخر دخول": 120
     }

     for col in cols:
          tree.heading(col, text=col)
          tree.column(col, width=col_widths.get(col, 120), anchor="center")

     scroll = ttk.Scrollbar(tree_container, orient="vertical", command=tree.yview)
     tree.configure(yscrollcommand=scroll.set)
     scroll.pack(side="left", fill="y")
     tree.pack(side="right", fill="both", expand=True)

     # تعبئة الجدول بالبيانات
     def load_teachers_data():
          for item in tree.get_children():
               tree.delete(item)

          teachers = get_teachers_in_center(center_name)
          for teacher in teachers:
               user = next((u for u in USERS if u.get("username") == teacher.get("username")), None)
               if user:
                    status = "نشط" if user.get("active", True) else "غير نشط"
                    status_color = COLORS["success"] if status == "نشط" else COLORS["danger"]

                    tree.insert("", "end", values=(
                         teacher.get("teacher_id", "غير معروف"),
                         user.get("full_name", teacher.get("username", "")),
                         user.get("phone", ""),
                         user.get("email", ""),
                         len(teacher.get("students", [])),
                         status,
                         user.get("last_login", "لم يسجل دخول")
                    ), tags=(status,))

          tree.tag_configure("نشط", background=COLORS["light"])
          tree.tag_configure("غير نشط", background="#ffdddd")

     load_teachers_data()

     # وظيفة البحث والتصفية
     def filter_teachers(*args):
          search_text = search_var.get().lower()
          status_filter = status_var.get()

          for item in tree.get_children():
               tree.delete(item)

          teachers = get_teachers_in_center(center_name)
          for teacher in teachers:
               user = next((u for u in USERS if u.get("username") == teacher.get("username")), None)
               if user:
                    status = "نشط" if user.get("active", True) else "غير نشط"

                    if ((status_filter == "الكل" or status == status_filter) and
                             (search_text in user.get("full_name", "").lower() or
                              search_text in teacher.get("teacher_id", "").lower() or
                              search_text in user.get("phone", "").lower())):
                         tree.insert("", "end", values=(
                              teacher.get("teacher_id", "غير معروف"),
                              user.get("full_name", teacher.get("username", "")),
                              user.get("phone", ""),
                              user.get("email", ""),
                              len(teacher.get("students", [])),
                              status,
                              user.get("last_login", "لم يسجل دخول")
                         ), tags=(status,))

     search_var.trace("w", filter_teachers)
     status_var.trace("w", filter_teachers)

     # ========== أزرار التحكم السفلية ==========
     control_frame = tk.Frame(tab)
     control_frame.pack(fill="x", pady=(0, 10))

     def edit_selected():
          selected = tree.selection()
          if not selected:
               messagebox.showerror("خطأ", "يرجى اختيار معلم لتعديله")
               return

          teacher_id = tree.item(selected[0])["values"][0]

          # البحث عن المعلم وإضافة teacher_id إذا لم يكن موجودًا
          teacher = None
          for t in TEACHERS:
               if "teacher_id" not in t:
                    t["teacher_id"] = f"TEA{len(TEACHERS) + 1:03d}"  # إنشاء معرف فريد
               if t["teacher_id"] == teacher_id:
                    teacher = t
                    break

          if not teacher:
               messagebox.showerror("خطأ", "لم يتم العثور على المعلم المحدد")
               return

          user = next((u for u in USERS if u.get("username") == teacher.get("username")), None)
          if not user:
               messagebox.showerror("خطأ", "لم يتم العثور على بيانات المستخدم")
               return

          dialog = tk.Toplevel()
          dialog.title(f"تعديل معلم {teacher.get('full_name', '')}")
          dialog.geometry("700x600")
          center_window(dialog, 700, 600)

          # إطار التمرير للنموذج
          main_frame = tk.Frame(dialog)
          main_frame.pack(fill="both", expand=True)

          canvas = tk.Canvas(main_frame)
          scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
          scrollable_frame = tk.Frame(canvas)

          scrollable_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
          canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
          canvas.configure(yscrollcommand=scrollbar.set)

          scrollbar.pack(side="left", fill="y")
          canvas.pack(side="right", fill="both", expand=True)

          tk.Label(scrollable_frame, text=f"تعديل معلم {teacher.get('full_name', '')}",
                   font=FONTS["subtitle"], fg=COLORS["primary"]).pack(pady=10)

          fields = [
               {"label": "رقم المعلم", "value": teacher.get("teacher_id", ""), "type": "label"},
               {"label": "اسم المستخدم", "value": teacher.get("username", ""), "type": "label"},
               {"label": "الاسم الكامل", "value": user.get("full_name", ""), "type": "entry", "required": True},
               {"label": "تاريخ الميلاد", "value": user.get("birth_date", ""), "type": "date"},
               {"label": "رقم الهاتف", "value": user.get("phone", ""), "type": "entry", "validation": "phone"},
               {"label": "البريد الإلكتروني", "value": user.get("email", ""), "type": "entry", "validation": "email"},
               {"label": "العنوان", "value": user.get("address", ""), "type": "entry"},
               {"label": "الحالة الاجتماعية", "value": user.get("marital_status", ""), "type": "combobox",
                "options": ["أعزب", "متزوج", "مطلق", "أرمل"]},
               {"label": "المؤهل العلمي", "value": teacher.get("qualification", ""), "type": "entry"},
               {"label": "تاريخ التعيين", "value": teacher.get("hire_date", ""), "type": "date"},
               {"label": "الراتب الأساسي", "value": teacher.get("base_salary", ""), "type": "entry", "validation": "number"},
               {"label": "حالة الحساب", "value": "نشط" if user.get("active", True) else "غير نشط", "type": "combobox",
                "options": ["نشط", "غير نشط"]}
          ]

          entries = []
          for field in fields:
               frame = tk.Frame(scrollable_frame)
               frame.pack(fill="x", padx=20, pady=5)

               tk.Label(frame, text=field["label"] + ":", font=FONTS["small"],
                        width=20, anchor="e").pack(side="right")

               if field["type"] == "combobox":
                    var = tk.StringVar(value=field["value"])
                    combo = ttk.Combobox(frame, textvariable=var,
                                         values=field["options"],
                                         font=FONTS["small"], state="readonly")
                    combo.pack(fill="x")
                    entries.append(var)
               elif field["type"] == "date":
                    entry = ttk.Entry(frame, font=FONTS["small"])
                    entry.insert(0, field["value"])
                    entry.pack(fill="x")
                    entries.append(entry)
               elif field["type"] == "label":
                    tk.Label(frame, text=field["value"], font=FONTS["small"],
                             anchor="w").pack(fill="x")
               else:
                    entry = ttk.Entry(frame, font=FONTS["small"])
                    entry.insert(0, field["value"])
                    entry.pack(fill="x")
                    entries.append(entry)

          # إطار كلمة المرور (منفصل)
          password_frame = tk.Frame(scrollable_frame, bd=1, relief="solid", padx=10, pady=10)
          password_frame.pack(fill="x", padx=20, pady=10)

          tk.Label(password_frame, text="تغيير كلمة المرور",
                   font=FONTS["small"], fg=COLORS["primary"]).pack(anchor="w")

          tk.Label(password_frame, text="كلمة المرور الجديدة:",
                   font=FONTS["small"]).pack(anchor="w", pady=5)
          new_pass_entry = ttk.Entry(password_frame, font=FONTS["small"], show="*")
          new_pass_entry.pack(fill="x", pady=5)

          tk.Label(password_frame, text="تأكيد كلمة المرور:",
                   font=FONTS["small"]).pack(anchor="w", pady=5)
          confirm_pass_entry = ttk.Entry(password_frame, font=FONTS["small"], show="*")
          confirm_pass_entry.pack(fill="x", pady=5)

          def update_teacher():
               # التحقق من الحقول المطلوبة
               if not entries[2].get():  # الاسم الكامل مطلوب
                    messagebox.showerror("خطأ", "الاسم الكامل مطلوب")
                    return

               # التحقق من كلمة المرور إذا تم إدخالها
               new_pass = new_pass_entry.get()
               confirm_pass = confirm_pass_entry.get()

               if new_pass or confirm_pass:
                    if new_pass != confirm_pass:
                         messagebox.showerror("خطأ", "كلمتا المرور غير متطابقتين")
                         return
                    if len(new_pass) < 4:
                         messagebox.showerror("خطأ", "كلمة المرور يجب أن تكون 4 أحرف على الأقل")
                         return
                    user["password"] = new_pass

               # تحديث بيانات المعلم
               teacher.update({
                    "full_name": entries[2].get(),
                    "birth_date": entries[3].get(),
                    "phone": entries[4].get(),
                    "email": entries[5].get(),
                    "address": entries[6].get(),
                    "marital_status": entries[7].get(),
                    "qualification": entries[8].get(),
                    "hire_date": entries[9].get(),
                    "base_salary": entries[10].get()
               })

               # تحديث بيانات المستخدم
               user.update({
                    "full_name": entries[2].get(),
                    "birth_date": entries[3].get(),
                    "phone": entries[4].get(),
                    "email": entries[5].get(),
                    "address": entries[6].get(),
                    "marital_status": entries[7].get(),
                    "active": entries[11].get() == "نشط"
               })

               save_data()
               dialog.destroy()
               messagebox.showinfo("تم", "تم تحديث بيانات المعلم بنجاح")
               refresh()

          btn_frame = tk.Frame(scrollable_frame)
          btn_frame.pack(fill="x", pady=20)

          tk.Button(btn_frame, text="حفظ التعديلات", font=FONTS["normal"], bg=COLORS["success"],
                    fg="white", command=update_teacher).pack(side="right", padx=10, ipadx=20)
          tk.Button(btn_frame, text="إلغاء", font=FONTS["normal"], bg=COLORS["danger"],
                    fg="white", command=dialog.destroy).pack(side="left", padx=10, ipadx=20)

     def delete_selected():
          selected = tree.selection()
          if not selected:
               messagebox.showerror("خطأ", "يرجى اختيار معلم لحذفه")
               return

          teacher_id = tree.item(selected[0])["values"][0]
          teacher_name = tree.item(selected[0])["values"][1]

          if messagebox.askyesno("تأكيد الحذف",
                                 f"هل أنت متأكد من حذف المعلم {teacher_name}؟\nسيتم حذف جميع بياناته أيضاً"):
               global USERS, TEACHERS

               # حذف المعلم
               teacher = next((t for t in TEACHERS if t["teacher_id"] == teacher_id), None)
               if teacher:
                    # حذف المستخدم المرتبط
                    USERS = [u for u in USERS if u.get("username") != teacher.get("username")]

                    # حذف المعلم من القائمة
                    TEACHERS = [t for t in TEACHERS if t["teacher_id"] != teacher_id]

                    save_data()
                    messagebox.showinfo("تم", f"تم حذف المعلم {teacher_name} وبياناته بنجاح")
                    refresh()

     def toggle_status():
          selected = tree.selection()
          if not selected:
               messagebox.showerror("خطأ", "يرجى اختيار معلم")
               return

          teacher_id = tree.item(selected[0])["values"][0]
          teacher_name = tree.item(selected[0])["values"][1]
          current_status = tree.item(selected[0])["values"][5]

          new_status = "غير نشط" if current_status == "نشط" else "نشط"

          if messagebox.askyesno("تأكيد", f"هل تريد تغيير حالة المعلم {teacher_name} إلى {new_status}؟"):
               teacher = next((t for t in TEACHERS if t["teacher_id"] == teacher_id), None)
               if teacher:
                    user = next((u for u in USERS if u.get("username") == teacher.get("username")), None)
                    if user:
                         user["active"] = new_status == "نشط"
                         save_data()
                         messagebox.showinfo("تم", f"تم تغيير حالة المعلم إلى {new_status}")
                         refresh()

     # أزرار التحكم
     tk.Button(control_frame, text="تعديل المحدد", font=FONTS["small"], bg=COLORS["warning"],
               fg="white", command=edit_selected).pack(side="right", padx=10)
     tk.Button(control_frame, text="حذف المحدد", font=FONTS["small"], bg=COLORS["danger"],
               fg="white", command=delete_selected).pack(side="right", padx=10)
     tk.Button(control_frame, text="تفعيل/تعطيل", font=FONTS["small"], bg=COLORS["info"],
               fg="white", command=toggle_status).pack(side="right", padx=10)
     tk.Button(control_frame, text="عرض التفاصيل", font=FONTS["small"], bg=COLORS["primary"],
               fg="white").pack(side="right", padx=10)

def show_center_students_tab(tab, center_name):
     tab.pack_propagate(False)

     # تنظيف المحتوى السابق إذا وجد
     for widget in tab.winfo_children():
          widget.destroy()

     # شريط الأدوات
     toolbar = tk.Frame(tab, bg=COLORS["light"], pady=5)
     toolbar.pack(fill="x")

     tk.Label(toolbar, text=f"إدارة الطلاب - {center_name}", font=FONTS["subtitle"], bg=COLORS["light"]).pack(side="right",
                                                                                                              padx=10)

     def refresh():
          show_center_students_tab(tab, center_name)

     def add_new_student():
          dialog = tk.Toplevel()
          dialog.title("إضافة طالب جديد")
          dialog.geometry("700x800")  # زيادة حجم النافذة لاستيعاب الحقول الجديدة
          center_window(dialog, 700, 800)

          # إطار التمرير
          main_frame = tk.Frame(dialog)
          main_frame.pack(fill="both", expand=True)

          canvas = tk.Canvas(main_frame)
          scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
          scrollable_frame = tk.Frame(canvas)

          scrollable_frame.bind(
               "<Configure>",
               lambda e: canvas.configure(
                    scrollregion=canvas.bbox("all")
               )
          )

          canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
          canvas.configure(yscrollcommand=scrollbar.set)

          canvas.pack(side="left", fill="both", expand=True)
          scrollbar.pack(side="right", fill="y")

          tk.Label(scrollable_frame, text="إضافة طالب جديد", font=FONTS["subtitle"], fg=COLORS["primary"]).pack(pady=10)

          # الحصول على قائمة المعلمين في المركز
          teachers = [t["username"] for t in get_teachers_in_center(center_name)]
          if not teachers:
               messagebox.showerror("خطأ", "لا يوجد معلمون في هذا المركز. يرجى إضافة معلم أولاً")
               return

          # الحقول الجديدة المطلوبة
          fields = [
               ("الاسم الكامل", ""),
               ("تاريخ الميلاد (YYYY-MM-DD)", ""),
               ("المستوى", ["مبتدئ", "متوسط", "متقدم"]),
               ("السورة الحالية", ""),
               ("الأداء العام", ["ممتاز", "جيد جدًا", "جيد", "مقبول"]),
               ("خطة الحفظ", ["سنة واحدة", "سنتين", "3 سنوات", "4 سنوات"]),
               ("الفترة", ["صباحية", "مسائية"]),
               ("اسم ولي الأمر", ""),
               ("صلة القرابة", ["أب", "أم", "وصي", "أخ", "قريب"]),
               ("رقم ولي الأمر", ""),
               ("عنوان ولي الأمر", ""),
               ("بريد ولي الأمر الإلكتروني", ""),
               ("المهنة", ""),
               ("تاريخ الانضمام", get_today_date()),
               ("ملاحظات", ""),
               ("المعلم", teachers)
          ]

          entries = []
          for label, default in fields:
               frame = tk.Frame(scrollable_frame)
               frame.pack(fill="x", padx=20, pady=5)

               tk.Label(frame, text=label + ":", font=FONTS["small"], width=20, anchor="e").pack(side="right")

               if isinstance(default, list):
                    var = tk.StringVar()
                    combo = ttk.Combobox(frame, textvariable=var, values=default, font=FONTS["small"], state="readonly")
                    combo.pack(side="right", fill="x", expand=True)
                    entries.append(var)
               else:
                    entry = ttk.Entry(frame, font=FONTS["small"])
                    entry.insert(0, default)
                    entry.pack(side="right", fill="x", expand=True)
                    entries.append(entry)

          def save_student():
               # التحقق من الحقول المطلوبة
               required_fields = [0, 1, 2, 7, 9,
                                  10]  # حقول مطلوبة (الاسم، تاريخ الميلاد، المستوى، اسم الوالد، رقم الوالد، العنوان)
               for i in required_fields:
                    if not entries[i].get():
                         messagebox.showerror("خطأ", "جميع الحقول المطلوبة يجب تعبئتها")
                         return

               # إنشاء رقم هوية الطالب
               student_id = f"STU{len(get_all_students()) + 1:03d}"

               # إنشاء كائن الطالب الجديد
               new_student = {
                    "student_id": student_id,
                    "name": entries[0].get(),
                    "birth_date": entries[1].get(),
                    "level": entries[2].get(),
                    "current_surah": entries[3].get(),
                    "performance": entries[4].get(),
                    "plan": entries[5].get(),
                    "period": entries[6].get(),
                    "parent_name": entries[7].get(),
                    "parent_relation": entries[8].get(),
                    "parent_contact": entries[9].get(),
                    "parent_address": entries[10].get(),
                    "parent_email": entries[11].get(),
                    "parent_job": entries[12].get(),
                    "join_date": entries[13].get(),
                    "notes": entries[14].get(),
                    "recitations": [],
                    "attendance": []
               }

               # البحث عن المعلم وإضافة الطالب إليه
               teacher_username = entries[15].get()
               teacher = next((t for t in TEACHERS if t["username"] == teacher_username), None)

               if teacher:
                    if "students" not in teacher:
                         teacher["students"] = []
                    teacher["students"].append(new_student)

                    # حفظ التغييرات في الملفات
                    save_data()

                    dialog.destroy()
                    messagebox.showinfo("تم", "تم إضافة الطالب بنجاح")
                    refresh()
               else:
                    messagebox.showerror("خطأ", "لم يتم العثور على المعلم المحدد")

          btn_frame = tk.Frame(scrollable_frame)
          btn_frame.pack(fill="x", pady=20)

          tk.Button(btn_frame, text="حفظ", font=FONTS["normal"], bg=COLORS["success"], fg="white",
                    command=save_student).pack(side="right", padx=10)
          tk.Button(btn_frame, text="إلغاء", font=FONTS["normal"], bg=COLORS["danger"], fg="white",
                    command=dialog.destroy).pack(side="left", padx=10)

     tk.Button(toolbar, text="إضافة طالب", font=FONTS["small"], bg=COLORS["success"], fg="white",
               command=add_new_student).pack(side="left", padx=10)
     tk.Button(toolbar, text="تحديث", font=FONTS["small"], bg=COLORS["info"], fg="white", command=refresh).pack(side="left",
                                                                                                                padx=10)

     # إطار البحث والتصفية
     filter_frame = tk.Frame(tab, bg="white", padx=10, pady=10)
     filter_frame.pack(fill="x")

     tk.Label(filter_frame, text="بحث:", font=FONTS["small"], bg="white").pack(side="right")
     search_var = tk.StringVar()
     search_entry = tk.Entry(filter_frame, textvariable=search_var, font=FONTS["small"], width=30)
     search_entry.pack(side="right", padx=5)

     tk.Label(filter_frame, text="تصفية حسب المعلم:", font=FONTS["small"], bg="white").pack(side="right", padx=10)
     teacher_var = tk.StringVar()
     teacher_combo = ttk.Combobox(filter_frame, textvariable=teacher_var,
                                  values=["الكل"] + [t["username"] for t in get_teachers_in_center(center_name)],
                                  font=FONTS["small"], state="readonly", width=20)
     teacher_combo.current(0)
     teacher_combo.pack(side="right")

     # جدول الطلاب مع أعمدة إضافية
     tree_frame = tk.Frame(tab)
     tree_frame.pack(fill="both", expand=True, padx=10, pady=10)

     cols = ("رقم الطالب", "الاسم", "تاريخ الميلاد", "المستوى", "السورة", "الأداء", "المعلم", "تاريخ الانضمام")
     tree = ttk.Treeview(tree_frame, columns=cols, show="headings", style="Custom.Treeview")

     for col in cols:
          tree.heading(col, text=col)
          tree.column(col, width=100, anchor="center")

     scroll = ttk.Scrollbar(tree_frame, orient="vertical", command=tree.yview)
     tree.configure(yscrollcommand=scroll.set)
     scroll.pack(side="left", fill="y")
     tree.pack(side="right", fill="both", expand=True)

     students = get_all_students_in_center(center_name)
     for s in students:
          tree.insert("", "end", values=(
               s.get("student_id", ""),
               s["name"],
               s.get("birth_date", ""),
               s["level"],
               s["current_surah"],
               s["performance"],
               s["teacher"],
               s.get("join_date", "")
          ))

     def search_students(*args):
          search_text = search_var.get().lower()
          teacher_filter = teacher_var.get()

          for item in tree.get_children():
               tree.delete(item)

          for s in students:
               if (search_text in s["name"].lower() or
                   search_text in s["teacher"].lower() or
                   search_text in s.get("student_id", "").lower()) and \
                        (teacher_filter == "الكل" or s["teacher"] == teacher_filter):
                    tree.insert("", "end", values=(
                         s.get("student_id", ""),
                         s["name"],
                         s.get("birth_date", ""),
                         s["level"],
                         s["current_surah"],
                         s["performance"],
                         s["teacher"],
                         s.get("join_date", "")
                    ))

     search_var.trace("w", search_students)
     teacher_var.trace("w", search_students)

     def open_selected():
          selected = tree.selection()
          if not selected:
               messagebox.showerror("خطأ", "يرجى اختيار طالب")
               return

          student_id = tree.item(selected[0])["values"][0]
          teacher_name = tree.item(selected[0])["values"][6]

          teacher = next((t for t in TEACHERS if t["username"] == teacher_name), None)
          if teacher:
               student = next((s for s in teacher["students"] if s.get("student_id") == student_id), None)
               if student:
                    open_student_file(student)
               else:
                    messagebox.showerror("خطأ", "لم يتم العثور على بيانات الطالب")
          else:
               messagebox.showerror("خطأ", "لم يتم العثور على المعلم")

     def edit_selected():
          selected = tree.selection()
          if not selected:
               messagebox.showerror("خطأ", "يرجى اختيار طالب لتعديله")
               return

          student_id = tree.item(selected[0])["values"][0]
          teacher_name = tree.item(selected[0])["values"][6]

          teacher = next((t for t in TEACHERS if t["username"] == teacher_name), None)
          if not teacher:
               messagebox.showerror("خطأ", "لم يتم العثور على المعلم")
               return

          student = next((s for s in teacher["students"] if s.get("student_id") == student_id), None)
          if not student:
               messagebox.showerror("خطأ", "لم يتم العثور على الطالب")
               return

          dialog = tk.Toplevel()
          dialog.title("تعديل بيانات الطالب")
          dialog.geometry("700x800")
          center_window(dialog, 700, 800)

          # إطار التمرير
          main_frame = tk.Frame(dialog)
          main_frame.pack(fill="both", expand=True)

          canvas = tk.Canvas(main_frame)
          scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
          scrollable_frame = tk.Frame(canvas)

          scrollable_frame.bind(
               "<Configure>",
               lambda e: canvas.configure(
                    scrollregion=canvas.bbox("all")
               )
          )

          canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
          canvas.configure(yscrollcommand=scrollbar.set)

          canvas.pack(side="left", fill="both", expand=True)
          scrollbar.pack(side="right", fill="y")

          tk.Label(scrollable_frame, text="تعديل بيانات الطالب", font=FONTS["subtitle"], fg=COLORS["primary"]).pack(pady=10)

          # الحصول على قائمة المعلمين في المركز
          teachers = [t["username"] for t in get_teachers_in_center(center_name)]

          # الحقول للتعديل
          fields = [
               ("رقم الطالب", student.get("student_id", ""), False),
               ("الاسم الكامل", student.get("name", ""), False),
               ("تاريخ الميلاد", student.get("birth_date", ""), False),
               ("المستوى", student.get("level", ""), ["مبتدئ", "متوسط", "متقدم"]),
               ("السورة الحالية", student.get("current_surah", ""), False),
               ("الأداء العام", student.get("performance", ""), ["ممتاز", "جيد جدًا", "جيد", "مقبول"]),
               ("خطة الحفظ", student.get("plan", ""), ["سنة واحدة", "سنتين", "3 سنوات", "4 سنوات"]),
               ("الفترة", student.get("period", ""), ["صباحية", "مسائية"]),
               ("اسم ولي الأمر", student.get("parent_name", ""), False),
               ("صلة القرابة", student.get("parent_relation", ""), ["أب", "أم", "وصي", "أخ", "قريب"]),
               ("رقم ولي الأمر", student.get("parent_contact", ""), False),
               ("عنوان ولي الأمر", student.get("parent_address", ""), False),
               ("بريد ولي الأمر", student.get("parent_email", ""), False),
               ("مهنة ولي الأمر", student.get("parent_job", ""), False),
               ("تاريخ الانضمام", student.get("join_date", ""), False),
               ("ملاحظات", student.get("notes", ""), False),
               ("المعلم", student.get("teacher", ""), teachers)
          ]

          entries = []
          for label, default, options in fields:
               frame = tk.Frame(scrollable_frame)
               frame.pack(fill="x", padx=20, pady=5)

               tk.Label(frame, text=label + ":", font=FONTS["small"], width=20, anchor="e").pack(side="right")

               if isinstance(options, list):
                    var = tk.StringVar(value=default)
                    combo = ttk.Combobox(frame, textvariable=var, values=options, font=FONTS["small"], state="readonly")
                    combo.pack(side="right", fill="x", expand=True)
                    entries.append(var)
               else:
                    entry = ttk.Entry(frame, font=FONTS["small"])
                    entry.insert(0, default)
                    entry.pack(side="right", fill="x", expand=True)
                    entries.append(entry)

          def update_student():
               # التحقق من الحقول المطلوبة
               required_fields = [1, 2, 3, 8, 10, 11]  # حقول مطلوبة
               for i in required_fields:
                    if not entries[i].get():
                         messagebox.showerror("خطأ", "جميع الحقول المطلوبة يجب تعبئتها")
                         return

               # تحديث بيانات الطالب
               student["name"] = entries[1].get()
               student["birth_date"] = entries[2].get()
               student["level"] = entries[3].get()
               student["current_surah"] = entries[4].get()
               student["performance"] = entries[5].get()
               student["plan"] = entries[6].get()
               student["period"] = entries[7].get()
               student["parent_name"] = entries[8].get()
               student["parent_relation"] = entries[9].get()
               student["parent_contact"] = entries[10].get()
               student["parent_address"] = entries[11].get()
               student["parent_email"] = entries[12].get()
               student["parent_job"] = entries[13].get()
               student["join_date"] = entries[14].get()
               student["notes"] = entries[15].get()

               # إذا تم تغيير المعلم
               new_teacher = entries[16].get()
               if new_teacher != student.get("teacher", ""):
                    # إزالة الطالب من المعلم القديم
                    old_teacher = next((t for t in TEACHERS if t["username"] == student.get("teacher", "")), None)
                    if old_teacher:
                         old_teacher["students"] = [s for s in old_teacher["students"] if
                                                    s.get("student_id") != student.get("student_id", "")]

                    # إضافة الطالب إلى المعلم الجديد
                    teacher = next((t for t in TEACHERS if t["username"] == new_teacher), None)
                    if teacher:
                         if "students" not in teacher:
                              teacher["students"] = []
                         teacher["students"].append(student)
                         student["teacher"] = new_teacher

               save_data()  # حفظ التغييرات
               dialog.destroy()
               messagebox.showinfo("تم", "تم تحديث بيانات الطالب بنجاح")
               refresh()

          btn_frame = tk.Frame(scrollable_frame)
          btn_frame.pack(fill="x", pady=20)

          tk.Button(btn_frame, text="حفظ التعديلات", font=FONTS["normal"], bg=COLORS["success"], fg="white",
                    command=update_student).pack(side="right", padx=10)
          tk.Button(btn_frame, text="إلغاء", font=FONTS["normal"], bg=COLORS["danger"], fg="white",
                    command=dialog.destroy).pack(side="left", padx=10)

     def delete_selected():
          selected = tree.selection()
          if not selected:
               messagebox.showerror("خطأ", "يرجى اختيار طالب لحذفه")
               return

          student_id = tree.item(selected[0])["values"][0]
          teacher_name = tree.item(selected[0])["values"][6]

          if messagebox.askyesno("تأكيد", f"هل أنت متأكد من حذف الطالب {tree.item(selected[0])['values'][1]}؟"):
               teacher = next((t for t in TEACHERS if t["username"] == teacher_name), None)
               if teacher:
                    teacher["students"] = [s for s in teacher["students"] if s.get("student_id") != student_id]
                    save_data()  # حفظ التغييرات
                    messagebox.showinfo("تم", "تم حذف الطالب بنجاح")
                    refresh()
               else:
                    messagebox.showerror("خطأ", "لم يتم العثور على المعلم")

     def transfer_student():
          selected = tree.selection()
          if not selected:
               messagebox.showerror("خطأ", "يرجى اختيار طالب لنقله")
               return

          student_id = tree.item(selected[0])["values"][0]
          current_teacher = tree.item(selected[0])["values"][6]

          # الحصول على قائمة المعلمين المتاحين (باستثناء المعلم الحالي)
          available_teachers = [t["username"] for t in get_teachers_in_center(center_name) if
                                t["username"] != current_teacher]

          if not available_teachers:
               messagebox.showerror("خطأ", "لا يوجد معلمون آخرون في المركز")
               return

          dialog = tk.Toplevel()
          dialog.title("نقل الطالب إلى معلم آخر")
          dialog.geometry("400x200")
          center_window(dialog, 400, 200)

          tk.Label(dialog, text="اختر المعلم الجديد:", font=FONTS["normal"]).pack(pady=10)

          teacher_var = tk.StringVar()
          teacher_combo = ttk.Combobox(dialog, textvariable=teacher_var,
                                       values=available_teachers,
                                       font=FONTS["normal"], state="readonly")
          teacher_combo.pack(pady=10)

          def confirm_transfer():
               new_teacher = teacher_var.get()
               if not new_teacher:
                    messagebox.showerror("خطأ", "يجب اختيار معلم")
                    return

               # البحث عن الطالب في المعلم الحالي
               old_teacher = next((t for t in TEACHERS if t["username"] == current_teacher), None)
               student = None

               if old_teacher:
                    student = next((s for s in old_teacher["students"] if s.get("student_id") == student_id), None)

                    if student:
                         # إزالة الطالب من المعلم القديم
                         old_teacher["students"] = [s for s in old_teacher["students"] if s.get("student_id") != student_id]

                         # إضافة الطالب إلى المعلم الجديد
                         new_teacher_obj = next((t for t in TEACHERS if t["username"] == new_teacher), None)
                         if new_teacher_obj:
                              if "students" not in new_teacher_obj:
                                   new_teacher_obj["students"] = []
                              new_teacher_obj["students"].append(student)
                              student["teacher"] = new_teacher

                              save_data()  # حفظ التغييرات
                              dialog.destroy()
                              messagebox.showinfo("تم", "تم نقل الطالب بنجاح")
                              refresh()
                         else:
                              messagebox.showerror("خطأ", "لم يتم العثور على المعلم الجديد")
                    else:
                         messagebox.showerror("خطأ", "لم يتم العثور على الطالب")
               else:
                    messagebox.showerror("خطأ", "لم يتم العثور على المعلم الحالي")

          btn_frame = tk.Frame(dialog)
          btn_frame.pack(pady=20)

          tk.Button(btn_frame, text="تأكيد النقل", font=FONTS["normal"], bg=COLORS["success"], fg="white",
                    command=confirm_transfer).pack(side="right", padx=10)
          tk.Button(btn_frame, text="إلغاء", font=FONTS["normal"], bg=COLORS["danger"], fg="white",
                    command=dialog.destroy).pack(side="left", padx=10)

     # أزرار التحكم
     control_frame = tk.Frame(tab)
     control_frame.pack(fill="x", pady=10)

     tk.Button(control_frame, text="فتح ملف الطالب", font=FONTS["small"], bg=COLORS["primary"], fg="white",
               command=open_selected).pack(side="right", padx=10)
     tk.Button(control_frame, text="نقل إلى معلم آخر", font=FONTS["small"], bg=COLORS["info"], fg="white",
               command=transfer_student).pack(side="right", padx=10)
     tk.Button(control_frame, text="تعديل بيانات الطالب", font=FONTS["small"], bg=COLORS["warning"], fg="white",
               command=edit_selected).pack(side="right", padx=10)
     tk.Button(control_frame, text="حذف الطالب", font=FONTS["small"], bg=COLORS["danger"], fg="white",
               command=delete_selected).pack(side="right", padx=10)

def show_center_fees_tab(tab, center_name):
     tab.pack_propagate(False)

     # تعريف متغيرات البيانات
     global FEES_DATA
     FEES_FILE = "fees.json"

     # تحميل بيانات الرسوم من ملف
     def load_fees():
          global FEES_DATA
          try:
               with open(FEES_FILE, "r", encoding="utf-8") as f:
                    FEES_DATA = json.load(f)
          except (FileNotFoundError, json.JSONDecodeError):
               FEES_DATA = []
               # إنشاء بيانات أولية إذا لم يكن الملف موجوداً
               sample_fees = [
                    {"fee_id": "FEE001", "student": "محمد عبد الله", "type": "رسوم شهرية",
                     "amount": "200 ريال", "date": "2025-07-01", "status": "مدفوع",
                     "notes": "", "center": center_name},
                    {"fee_id": "FEE002", "student": "أحمد كمارا", "type": "رسوم تسجيل",
                     "amount": "100 ريال", "date": "2025-07-05", "status": "غير مدفوع",
                     "notes": "يتم الدفع غداً", "center": center_name}
               ]
               FEES_DATA.extend(sample_fees)
               save_fees()

     # حفظ بيانات الرسوم في ملف
     def save_fees():
          try:
               with open(FEES_FILE, "w", encoding="utf-8") as f:
                    json.dump(FEES_DATA, f, ensure_ascii=False, indent=2)
          except Exception as e:
               messagebox.showerror("خطأ في الحفظ", f"حدث خطأ أثناء حفظ بيانات الرسوم: {str(e)}")

     # تحميل البيانات أول مرة
     load_fees()

     # شريط الأدوات
     toolbar = tk.Frame(tab, bg=COLORS["light"], pady=5)
     toolbar.pack(fill="x")

     tk.Label(toolbar, text=f"إدارة الرسوم الدراسية - {center_name}",
              font=FONTS["subtitle"], bg=COLORS["light"]).pack(side="right", padx=10)

     def refresh():
          for widget in tab.winfo_children():
               widget.destroy()
          show_center_fees_tab(tab, center_name)

     def add_fee():
          dialog = tk.Toplevel()
          dialog.title("إضافة رسوم جديدة")
          dialog.geometry("600x500")  # زيادة الحجم لاستيعاب المزيد من الحقول
          center_window(dialog, 600, 500)

          # إطار التمرير للسماح بإضافة الكثير من الحقول
          main_frame = tk.Frame(dialog)
          main_frame.pack(fill="both", expand=True)

          canvas = tk.Canvas(main_frame)
          scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
          scrollable_frame = tk.Frame(canvas)

          scrollable_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
          canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
          canvas.configure(yscrollcommand=scrollbar.set)

          canvas.pack(side="left", fill="both", expand=True)
          scrollbar.pack(side="right", fill="y")

          tk.Label(scrollable_frame, text="إضافة رسوم جديدة",
                   font=FONTS["subtitle"], fg=COLORS["primary"]).pack(pady=10)

          # الحصول على قائمة الطلاب في المركز
          students = [s["name"] for s in get_all_students_in_center(center_name)]
          if not students:
               messagebox.showerror("خطأ", "لا يوجد طلاب مسجلين في هذا المركز")
               dialog.destroy()
               return

          fields = [
               ("اسم الطالب", students),
               ("نوع الرسوم", ["رسوم تسجيل", "رسوم شهرية", "رسوم مواد", "رسوم اختبارات", "رسوم أخرى"]),
               ("المبلغ (ريال)", ""),
               ("التاريخ", get_today_date()),
               ("حالة الدفع", ["مدفوع", "غير مدفوع", "جزئي"]),
               ("طريقة الدفع", ["نقدي", "تحويل بنكي", "شيك", "بطاقة ائتمان"]),
               ("ملاحظات", "")
          ]

          entries = []
          for label, default in fields:
               frame = tk.Frame(scrollable_frame)
               frame.pack(fill="x", padx=20, pady=5)

               tk.Label(frame, text=label + ":", font=FONTS["small"], width=15, anchor="e").pack(side="right")

               if isinstance(default, list):
                    var = tk.StringVar()
                    combo = ttk.Combobox(frame, textvariable=var, values=default,
                                         font=FONTS["small"], state="readonly")
                    combo.pack(side="right", fill="x", expand=True)
                    entries.append(var)
               else:
                    entry = ttk.Entry(frame, font=FONTS["small"])
                    entry.insert(0, default)
                    entry.pack(side="right", fill="x", expand=True)
                    entries.append(entry)

          def save_fee():
               # التحقق من الحقول المطلوبة
               required_fields = [0, 1, 2, 3]  # حقول مطلوبة
               for i in required_fields:
                    if not entries[i].get():
                         messagebox.showerror("خطأ", "جميع الحقول المطلوبة يجب تعبئتها")
                         return

               try:
                    amount = float(entries[2].get())
               except ValueError:
                    messagebox.showerror("خطأ", "يجب إدخال مبلغ صحيح")
                    return

               # إنشاء رقم فريد للرسوم
               fee_id = f"FEE{len(FEES_DATA) + 1:03d}"

               new_fee = {
                    "fee_id": fee_id,
                    "student": entries[0].get(),
                    "type": entries[1].get(),
                    "amount": f"{amount} ريال",
                    "date": entries[3].get(),
                    "status": entries[4].get(),
                    "payment_method": entries[5].get(),
                    "notes": entries[6].get(),
                    "center": center_name,
                    "created_at": f"{get_today_date()} {get_current_time()}"
               }

               FEES_DATA.append(new_fee)
               save_fees()

               dialog.destroy()
               messagebox.showinfo("تم", f"تم إضافة الرسوم بنجاح (رقم الفاتورة: {fee_id})")
               refresh()

          btn_frame = tk.Frame(scrollable_frame)
          btn_frame.pack(fill="x", pady=10)

          tk.Button(btn_frame, text="حفظ", font=FONTS["normal"], bg=COLORS["success"],
                    fg="white", command=save_fee).pack(side="right", padx=10, ipadx=20)
          tk.Button(btn_frame, text="إلغاء", font=FONTS["normal"], bg=COLORS["danger"],
                    fg="white", command=dialog.destroy).pack(side="left", padx=10, ipadx=20)

     def edit_fee():
          selected = tree.selection()
          if not selected:
               messagebox.showerror("خطأ", "يرجى اختيار رسوم لتعديلها")
               return

          fee_id = tree.item(selected[0])["values"][0]
          fee = next((f for f in FEES_DATA if f["fee_id"] == fee_id), None)

          if not fee:
               messagebox.showerror("خطأ", "لم يتم العثور على بيانات الرسوم المحددة")
               return

          dialog = tk.Toplevel()
          dialog.title("تعديل بيانات الرسوم")
          dialog.geometry("600x500")
          center_window(dialog, 600, 500)

          main_frame = tk.Frame(dialog)
          main_frame.pack(fill="both", expand=True)

          canvas = tk.Canvas(main_frame)
          scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
          scrollable_frame = tk.Frame(canvas)

          scrollable_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
          canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
          canvas.configure(yscrollcommand=scrollbar.set)

          canvas.pack(side="left", fill="both", expand=True)
          scrollbar.pack(side="right", fill="y")

          tk.Label(scrollable_frame, text=f"تعديل رسوم رقم {fee_id}",
                   font=FONTS["subtitle"], fg=COLORS["primary"]).pack(pady=10)

          fields = [
               ("اسم الطالب", fee["student"], False),
               ("نوع الرسوم", fee["type"], ["رسوم تسجيل", "رسوم شهرية", "رسوم مواد", "رسوم اختبارات", "رسوم أخرى"]),
               ("المبلغ (ريال)", fee["amount"].replace(" ريال", ""), False),
               ("التاريخ", fee["date"], False),
               ("حالة الدفع", fee["status"], ["مدفوع", "غير مدفوع", "جزئي"]),
               ("طريقة الدفع", fee.get("payment_method", ""), ["نقدي", "تحويل بنكي", "شيك", "بطاقة ائتمان"]),
               ("ملاحظات", fee.get("notes", ""), False)
          ]

          entries = []
          for label, default, options in fields:
               frame = tk.Frame(scrollable_frame)
               frame.pack(fill="x", padx=20, pady=5)

               tk.Label(frame, text=label + ":", font=FONTS["small"], width=15, anchor="e").pack(side="right")

               if isinstance(options, list):
                    var = tk.StringVar(value=default)
                    combo = ttk.Combobox(frame, textvariable=var, values=options,
                                         font=FONTS["small"], state="readonly")
                    combo.pack(side="right", fill="x", expand=True)
                    entries.append(var)
               else:
                    entry = ttk.Entry(frame, font=FONTS["small"])
                    entry.insert(0, default)
                    entry.pack(side="right", fill="x", expand=True)
                    entries.append(entry)

          def update_fee():
               # التحقق من الحقول المطلوبة
               required_fields = [0, 1, 2, 3]  # حقول مطلوبة
               for i in required_fields:
                    if not entries[i].get():
                         messagebox.showerror("خطأ", "جميع الحقول المطلوبة يجب تعبئتها")
                         return

               try:
                    amount = float(entries[2].get())
               except ValueError:
                    messagebox.showerror("خطأ", "يجب إدخال مبلغ صحيح")
                    return

               # تحديث بيانات الرسوم
               fee.update({
                    "student": entries[0].get(),
                    "type": entries[1].get(),
                    "amount": f"{amount} ريال",
                    "date": entries[3].get(),
                    "status": entries[4].get(),
                    "payment_method": entries[5].get(),
                    "notes": entries[6].get(),
                    "updated_at": f"{get_today_date()} {get_current_time()}"
               })

               save_fees()
               dialog.destroy()
               messagebox.showinfo("تم", "تم تحديث بيانات الرسوم بنجاح")
               refresh()

          btn_frame = tk.Frame(scrollable_frame)
          btn_frame.pack(fill="x", pady=10)

          tk.Button(btn_frame, text="حفظ التعديلات", font=FONTS["normal"], bg=COLORS["success"],
                    fg="white", command=update_fee).pack(side="right", padx=10, ipadx=20)
          tk.Button(btn_frame, text="إلغاء", font=FONTS["normal"], bg=COLORS["danger"],
                    fg="white", command=dialog.destroy).pack(side="left", padx=10, ipadx=20)

     def delete_fee():
          selected = tree.selection()
          if not selected:
               messagebox.showerror("خطأ", "يرجى اختيار رسوم لحذفها")
               return

          fee_id = tree.item(selected[0])["values"][0]

          if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف الرسوم رقم {fee_id}؟"):
               global FEES_DATA
               FEES_DATA = [f for f in FEES_DATA if f["fee_id"] != fee_id]
               save_fees()
               messagebox.showinfo("تم", "تم حذف الرسوم بنجاح")
               refresh()

     def mark_as_paid():
          selected = tree.selection()
          if not selected:
               messagebox.showerror("خطأ", "يرجى اختيار رسوم لتحديدها كمدفوعة")
               return

          fee_id = tree.item(selected[0])["values"][0]
          fee = next((f for f in FEES_DATA if f["fee_id"] == fee_id), None)

          if not fee:
               messagebox.showerror("خطأ", "لم يتم العثور على بيانات الرسوم المحددة")
               return

          if fee["status"] == "مدفوع":
               messagebox.showwarning("تحذير", "الرسوم مسددة بالفعل")
               return

          fee["status"] = "مدفوع"
          fee["payment_method"] = "نقدي"  # يمكن تعديلها لاحقاً إذا لزم الأمر
          fee["updated_at"] = f"{get_today_date()} {get_current_time()}"

          save_fees()
          messagebox.showinfo("تم", "تم تحديث حالة الرسوم إلى مدفوع")
          refresh()

     def print_receipt():
          selected = tree.selection()
          if not selected:
               messagebox.showerror("خطأ", "يرجى اختيار رسوم لطباعة إيصالها")
               return

          fee_id = tree.item(selected[0])["values"][0]
          fee = next((f for f in FEES_DATA if f["fee_id"] == fee_id), None)

          if not fee:
               messagebox.showerror("خطأ", "لم يتم العثور على بيانات الرسوم المحددة")
               return

          # في الواقع، هنا سيتم الاتصال بطابعة أو إنشاء ملف PDF
          receipt_text = f"""
        إيصال دفع رسوم دراسية
        ======================
        رقم الفاتورة: {fee['fee_id']}
        اسم الطالب: {fee['student']}
        نوع الرسوم: {fee['type']}
        المبلغ: {fee['amount']}
        تاريخ الدفع: {fee['date']}
        طريقة الدفع: {fee.get('payment_method', 'غير محدد')}
        حالة الدفع: {fee['status']}
        ======================
        شكراً لثقتكم بنا
        مركز {center_name}
        """

          messagebox.showinfo("إيصال الدفع", receipt_text)

     def export_to_excel():
          file_path = filedialog.asksaveasfilename(
               defaultextension=".xlsx",
               filetypes=[("Excel Files", "*.xlsx"), ("All Files", "*.*")],
               title="حفظ كملف Excel"
          )

          if not file_path:
               return

          try:
               # إنشاء بيانات Excel (في الواقع سيتم استخدام مكتبة مثل openpyxl)
               # هذا مثال مبسط للتوضيح فقط
               excel_data = "رقم الفاتورة,اسم الطالب,نوع الرسوم,المبلغ,التاريخ,حالة الدفع\n"
               for fee in FEES_DATA:
                    if fee["center"] == center_name:
                         excel_data += f"{fee['fee_id']},{fee['student']},{fee['type']},{fee['amount']},{fee['date']},{fee['status']}\n"

               with open(file_path, "w", encoding="utf-8") as f:
                    f.write(excel_data)

               messagebox.showinfo("تم", f"تم تصدير البيانات إلى {file_path}")
          except Exception as e:
               messagebox.showerror("خطأ", f"حدث خطأ أثناء التصدير: {str(e)}")

     # أزرار التحكم
     tk.Button(toolbar, text="إضافة رسوم", font=FONTS["small"], bg=COLORS["success"],
               fg="white", command=add_fee).pack(side="left", padx=10)
     tk.Button(toolbar, text="تحديث", font=FONTS["small"], bg=COLORS["info"],
               fg="white", command=refresh).pack(side="left", padx=10)
     tk.Button(toolbar, text="تصدير Excel", font=FONTS["small"], bg=COLORS["primary"],
               fg="white", command=export_to_excel).pack(side="left", padx=10)

     # إطار البحث والتصفية
     filter_frame = tk.Frame(tab, bg="white", padx=10, pady=10)
     filter_frame.pack(fill="x")

     tk.Label(filter_frame, text="بحث:", font=FONTS["small"], bg="white").pack(side="right")
     search_var = tk.StringVar()
     search_entry = tk.Entry(filter_frame, textvariable=search_var, font=FONTS["small"], width=30)
     search_entry.pack(side="right", padx=5)

     tk.Label(filter_frame, text="تصفية حسب حالة الدفع:", font=FONTS["small"], bg="white").pack(side="right", padx=10)
     status_var = tk.StringVar()
     status_combo = ttk.Combobox(filter_frame, textvariable=status_var,
                                 values=["الكل", "مدفوع", "غير مدفوع", "جزئي"],
                                 font=FONTS["small"], state="readonly", width=15)
     status_combo.current(0)
     status_combo.pack(side="right")

     # جدول الرسوم
     tree_frame = tk.Frame(tab)
     tree_frame.pack(fill="both", expand=True, padx=10, pady=10)

     cols = ("رقم الفاتورة", "اسم الطالب", "نوع الرسوم", "المبلغ", "التاريخ", "حالة الدفع", "طريقة الدفع", "ملاحظات")
     tree = ttk.Treeview(tree_frame, columns=cols, show="headings", style="Custom.Treeview")

     for col in cols:
          tree.heading(col, text=col)
          tree.column(col, width=120, anchor="center")

     scroll = ttk.Scrollbar(tree_frame, orient="vertical", command=tree.yview)
     tree.configure(yscrollcommand=scroll.set)
     scroll.pack(side="left", fill="y")
     tree.pack(side="right", fill="both", expand=True)

     # عرض بيانات الرسوم
     for fee in FEES_DATA:
          if fee["center"] == center_name:
               tree.insert("", "end", values=(
                    fee["fee_id"],
                    fee["student"],
                    fee["type"],
                    fee["amount"],
                    fee["date"],
                    fee["status"],
                    fee.get("payment_method", ""),
                    fee.get("notes", "")
               ))

     def search_fees(*args):
          search_text = search_var.get().lower()
          status_filter = status_var.get()

          for item in tree.get_children():
               tree.delete(item)

          for fee in FEES_DATA:
               if fee["center"] == center_name:
                    if (search_text in fee["student"].lower() or
                        search_text in fee["type"].lower() or
                        search_text in fee["fee_id"].lower()) and \
                             (status_filter == "الكل" or fee["status"] == status_filter):
                         tree.insert("", "end", values=(
                              fee["fee_id"],
                              fee["student"],
                              fee["type"],
                              fee["amount"],
                              fee["date"],
                              fee["status"],
                              fee.get("payment_method", ""),
                              fee.get("notes", "")
                         ))

     search_var.trace("w", search_fees)
     status_var.trace("w", search_fees)

     # أزرار التحكم السفلية
     control_frame = tk.Frame(tab)
     control_frame.pack(fill="x", pady=10)

     tk.Button(control_frame, text="طباعة إيصال", font=FONTS["small"], bg=COLORS["primary"],
               fg="white", command=print_receipt).pack(side="right", padx=10)
     tk.Button(control_frame, text="تحديد كمدفوع", font=FONTS["small"], bg=COLORS["success"],
               fg="white", command=mark_as_paid).pack(side="right", padx=10)
     tk.Button(control_frame, text="تعديل الرسوم", font=FONTS["small"], bg=COLORS["warning"],
               fg="white", command=edit_fee).pack(side="right", padx=10)
     tk.Button(control_frame, text="حذف الرسوم", font=FONTS["small"], bg=COLORS["danger"],
               fg="white", command=delete_fee).pack(side="right", padx=10)

     # إحصائيات سريعة
     stats_frame = tk.Frame(tab, bg=COLORS["light"], pady=5)
     stats_frame.pack(fill="x")

     total_fees = sum(float(f["amount"].replace(" ريال", "")) for f in FEES_DATA if f["center"] == center_name)
     paid_fees = sum(float(f["amount"].replace(" ريال", "")) for f in FEES_DATA
                     if f["center"] == center_name and f["status"] == "مدفوع")
     unpaid_fees = total_fees - paid_fees

     tk.Label(stats_frame,
              text=f"إجمالي الرسوم: {total_fees} ريال | مدفوعة: {paid_fees} ريال | غير مدفوعة: {unpaid_fees} ريال",
              font=FONTS["small"], bg=COLORS["light"]).pack(side="right", padx=10)

def show_center_salaries_tab(tab, center_name):
     tab.pack_propagate(False)

     # تعريف متغيرات البيانات
     global SALARIES_DATA
     SALARIES_FILE = "salaries.json"

     # تحميل بيانات الرواتب من ملف
     def load_salaries():
          global SALARIES_DATA
          try:
               with open(SALARIES_FILE, "r", encoding="utf-8") as f:
                    SALARIES_DATA = json.load(f)
          except (FileNotFoundError, json.JSONDecodeError):
               SALARIES_DATA = []
               # إنشاء بيانات أولية إذا لم يكن الملف موجوداً
               sample_salaries = [
                    {"salary_id": "SAL001", "employee": "teacher1", "role": "معلم",
                     "type": "راتب أساسي", "amount": 3000, "currency": "ريال",
                     "month": "يوليو", "year": "2025", "status": "مدفوع",
                     "payment_method": "تحويل بنكي", "notes": "", "center": center_name,
                     "payment_date": "2025-07-01", "created_at": "2025-07-01 10:00"},
                    {"salary_id": "SAL002", "employee": "admin1", "role": "مدير المركز",
                     "type": "راتب أساسي", "amount": 5000, "currency": "ريال",
                     "month": "يوليو", "year": "2025", "status": "غير مدفوع",
                     "payment_method": "", "notes": "سيتم الدفع يوم 5 أغسطس",
                     "center": center_name, "payment_date": "", "created_at": "2025-07-01 10:00"}
               ]
               SALARIES_DATA.extend(sample_salaries)
               save_salaries()

     # حفظ بيانات الرواتب في ملف
     def save_salaries():
          try:
               with open(SALARIES_FILE, "w", encoding="utf-8") as f:
                    json.dump(SALARIES_DATA, f, ensure_ascii=False, indent=2)
          except Exception as e:
               messagebox.showerror("خطأ في الحفظ", f"حدث خطأ أثناء حفظ بيانات الرواتب: {str(e)}")

     # تحميل البيانات أول مرة
     load_salaries()

     # شريط الأدوات
     toolbar = tk.Frame(tab, bg=COLORS["light"], pady=5)
     toolbar.pack(fill="x")

     tk.Label(toolbar, text=f"إدارة الرواتب - {center_name}",
              font=FONTS["subtitle"], bg=COLORS["light"]).pack(side="right", padx=10)

     def refresh():
          for widget in tab.winfo_children():
               widget.destroy()
          show_center_salaries_tab(tab, center_name)

     def add_salary():
          dialog = tk.Toplevel()
          dialog.title("إضافة راتب جديد")
          dialog.geometry("700x600")  # زيادة الحجم لاستيعاب المزيد من الحقول
          center_window(dialog, 700, 600)

          # إطار التمرير للسماح بإضافة الكثير من الحقول
          main_frame = tk.Frame(dialog)
          main_frame.pack(fill="both", expand=True)

          canvas = tk.Canvas(main_frame)
          scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
          scrollable_frame = tk.Frame(canvas)

          scrollable_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
          canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
          canvas.configure(yscrollcommand=scrollbar.set)

          canvas.pack(side="left", fill="both", expand=True)
          scrollbar.pack(side="right", fill="y")

          tk.Label(scrollable_frame, text="إضافة راتب جديد",
                   font=FONTS["subtitle"], fg=COLORS["primary"]).pack(pady=10)

          # الحصول على قائمة الموظفين في المركز
          teachers = get_teachers_in_center(center_name)
          staff = [{"username": "admin1", "name": "مدير المركز", "role": "إدارة المركز"},
                   {"username": "accountant1", "name": "محاسب المركز", "role": "محاسب"}]

          employees = []
          for t in teachers:
               user = next((u for u in USERS if u["username"] == t["username"]), None)
               if user:
                    employees.append({
                         "username": t["username"],
                         "name": user.get("full_name", t["username"]),
                         "role": "معلم"
                    })

          employees.extend(staff)

          if not employees:
               messagebox.showerror("خطأ", "لا يوجد موظفون في هذا المركز")
               dialog.destroy()
               return

          fields = [
               ("اسم الموظف", [f"{e['name']} ({e['role']})" for e in employees]),
               ("نوع الراتب", ["راتب أساسي", "حافز", "مكافأة", "ساعات إضافية", "بدل نقل", "بدل سكن"]),
               ("المبلغ", ""),
               ("العملة", ["ريال", "دولار", "يورو"]),
               ("الشهر", ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                          "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"]),
               ("السنة", [str(year) for year in range(2023, 2026)]),
               ("حالة الدفع", ["غير مدفوع", "مدفوع", "جزئي"]),
               ("طريقة الدفع", ["نقدي", "تحويل بنكي", "شيك", "بطاقة ائتمان"]),
               ("تاريخ الدفع", get_today_date()),
               ("ملاحظات", "")
          ]

          entries = []
          for label, default in fields:
               frame = tk.Frame(scrollable_frame)
               frame.pack(fill="x", padx=20, pady=5)

               tk.Label(frame, text=label + ":", font=FONTS["small"], width=15, anchor="e").pack(side="right")

               if isinstance(default, list):
                    var = tk.StringVar()
                    combo = ttk.Combobox(frame, textvariable=var, values=default,
                                         font=FONTS["small"], state="readonly")
                    combo.pack(side="right", fill="x", expand=True)
                    entries.append(var)
               else:
                    entry = ttk.Entry(frame, font=FONTS["small"])
                    entry.insert(0, default)
                    entry.pack(side="right", fill="x", expand=True)
                    entries.append(entry)

          def save_salary():
               # التحقق من الحقول المطلوبة
               required_fields = [0, 1, 2, 3, 4, 5]  # حقول مطلوبة
               for i in required_fields:
                    if not entries[i].get():
                         messagebox.showerror("خطأ", "جميع الحقول المطلوبة يجب تعبئتها")
                         return

               try:
                    amount = float(entries[2].get())
                    if amount <= 0:
                         raise ValueError
               except ValueError:
                    messagebox.showerror("خطأ", "يجب إدخال مبلغ صحيح موجب")
                    return

               # استخراج اسم الموظف ودوره
               employee_info = entries[0].get().split(" (")
               employee_name = employee_info[0]
               employee_role = employee_info[1].replace(")", "")

               # إنشاء رقم فريد للراتب
               salary_id = f"SAL{len(SALARIES_DATA) + 1:03d}"

               new_salary = {
                    "salary_id": salary_id,
                    "employee": employee_name,
                    "username": next((e["username"] for e in employees if e["name"] == employee_name), ""),
                    "role": employee_role,
                    "type": entries[1].get(),
                    "amount": amount,
                    "currency": entries[3].get(),
                    "month": entries[4].get(),
                    "year": entries[5].get(),
                    "status": entries[6].get(),
                    "payment_method": entries[7].get() if entries[6].get() == "مدفوع" else "",
                    "payment_date": entries[8].get() if entries[6].get() == "مدفوع" else "",
                    "notes": entries[9].get(),
                    "center": center_name,
                    "created_at": f"{get_today_date()} {get_current_time()}"
               }

               SALARIES_DATA.append(new_salary)
               save_salaries()

               dialog.destroy()
               messagebox.showinfo("تم", f"تم إضافة الراتب بنجاح (رقم الراتب: {salary_id})")
               refresh()

          btn_frame = tk.Frame(scrollable_frame)
          btn_frame.pack(fill="x", pady=10)

          tk.Button(btn_frame, text="حفظ", font=FONTS["normal"], bg=COLORS["success"],
                    fg="white", command=save_salary).pack(side="right", padx=10, ipadx=20)
          tk.Button(btn_frame, text="إلغاء", font=FONTS["normal"], bg=COLORS["danger"],
                    fg="white", command=dialog.destroy).pack(side="left", padx=10, ipadx=20)

     def edit_salary():
          selected = tree.selection()
          if not selected:
               messagebox.showerror("خطأ", "يرجى اختيار راتب لتعديله")
               return

          salary_id = tree.item(selected[0])["values"][0]
          salary = next((s for s in SALARIES_DATA if s["salary_id"] == salary_id), None)

          if not salary:
               messagebox.showerror("خطأ", "لم يتم العثور على بيانات الراتب المحدد")
               return

          dialog = tk.Toplevel()
          dialog.title("تعديل بيانات الراتب")
          dialog.geometry("700x600")
          center_window(dialog, 700, 600)

          main_frame = tk.Frame(dialog)
          main_frame.pack(fill="both", expand=True)

          canvas = tk.Canvas(main_frame)
          scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
          scrollable_frame = tk.Frame(canvas)

          scrollable_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
          canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
          canvas.configure(yscrollcommand=scrollbar.set)

          canvas.pack(side="left", fill="both", expand=True)
          scrollbar.pack(side="right", fill="y")

          tk.Label(scrollable_frame, text=f"تعديل راتب رقم {salary_id}",
                   font=FONTS["subtitle"], fg=COLORS["primary"]).pack(pady=10)

          # الحصول على قائمة الموظفين
          teachers = get_teachers_in_center(center_name)
          staff = [{"username": "admin1", "name": "مدير المركز", "role": "إدارة المركز"},
                   {"username": "accountant1", "name": "محاسب المركز", "role": "محاسب"}]

          employees = []
          for t in teachers:
               user = next((u for u in USERS if u["username"] == t["username"]), None)
               if user:
                    employees.append({
                         "username": t["username"],
                         "name": user.get("full_name", t["username"]),
                         "role": "معلم"
                    })

          employees.extend(staff)

          fields = [
               ("اسم الموظف", f"{salary['employee']} ({salary['role']})",
                [f"{e['name']} ({e['role']})" for e in employees]),
               ("نوع الراتب", salary["type"],
                ["راتب أساسي", "حافز", "مكافأة", "ساعات إضافية", "بدل نقل", "بدل سكن"]),
               ("المبلغ", str(salary["amount"]), False),
               ("العملة", salary.get("currency", "ريال"), ["ريال", "دولار", "يورو"]),
               ("الشهر", salary["month"],
                ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                 "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"]),
               ("السنة", salary["year"], [str(year) for year in range(2023, 2026)]),
               ("حالة الدفع", salary["status"], ["غير مدفوع", "مدفوع", "جزئي"]),
               ("طريقة الدفع", salary.get("payment_method", ""),
                ["نقدي", "تحويل بنكي", "شيك", "بطاقة ائتمان"]),
               ("تاريخ الدفع", salary.get("payment_date", get_today_date()), False),
               ("ملاحظات", salary.get("notes", ""), False)
          ]

          entries = []
          for label, default, options in fields:
               frame = tk.Frame(scrollable_frame)
               frame.pack(fill="x", padx=20, pady=5)

               tk.Label(frame, text=label + ":", font=FONTS["small"], width=15, anchor="e").pack(side="right")

               if isinstance(options, list):
                    var = tk.StringVar(value=default)
                    combo = ttk.Combobox(frame, textvariable=var, values=options,
                                         font=FONTS["small"], state="readonly")
                    combo.pack(side="right", fill="x", expand=True)
                    entries.append(var)
               else:
                    entry = ttk.Entry(frame, font=FONTS["small"])
                    entry.insert(0, default)
                    entry.pack(side="right", fill="x", expand=True)
                    entries.append(entry)

          def update_salary():
               # التحقق من الحقول المطلوبة
               required_fields = [0, 1, 2, 3, 4, 5]  # حقول مطلوبة
               for i in required_fields:
                    if not entries[i].get():
                         messagebox.showerror("خطأ", "جميع الحقول المطلوبة يجب تعبئتها")
                         return

               try:
                    amount = float(entries[2].get())
                    if amount <= 0:
                         raise ValueError
               except ValueError:
                    messagebox.showerror("خطأ", "يجب إدخال مبلغ صحيح موجب")
                    return

               # استخراج اسم الموظف ودوره
               employee_info = entries[0].get().split(" (")
               employee_name = employee_info[0]
               employee_role = employee_info[1].replace(")", "")

               # تحديث بيانات الراتب
               salary.update({
                    "employee": employee_name,
                    "username": next((e["username"] for e in employees if e["name"] == employee_name), ""),
                    "role": employee_role,
                    "type": entries[1].get(),
                    "amount": amount,
                    "currency": entries[3].get(),
                    "month": entries[4].get(),
                    "year": entries[5].get(),
                    "status": entries[6].get(),
                    "payment_method": entries[7].get() if entries[6].get() == "مدفوع" else "",
                    "payment_date": entries[8].get() if entries[6].get() == "مدفوع" else "",
                    "notes": entries[9].get(),
                    "updated_at": f"{get_today_date()} {get_current_time()}"
               })

               save_salaries()
               dialog.destroy()
               messagebox.showinfo("تم", "تم تحديث بيانات الراتب بنجاح")
               refresh()

          btn_frame = tk.Frame(scrollable_frame)
          btn_frame.pack(fill="x", pady=10)

          tk.Button(btn_frame, text="حفظ التعديلات", font=FONTS["normal"], bg=COLORS["success"],
                    fg="white", command=update_salary).pack(side="right", padx=10, ipadx=20)
          tk.Button(btn_frame, text="إلغاء", font=FONTS["normal"], bg=COLORS["danger"],
                    fg="white", command=dialog.destroy).pack(side="left", padx=10, ipadx=20)

     def delete_salary():
          selected = tree.selection()
          if not selected:
               messagebox.showerror("خطأ", "يرجى اختيار راتب لحذفه")
               return

          salary_id = tree.item(selected[0])["values"][0]

          if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف الراتب رقم {salary_id}؟"):
               global SALARIES_DATA
               SALARIES_DATA = [s for s in SALARIES_DATA if s["salary_id"] != salary_id]
               save_salaries()
               messagebox.showinfo("تم", "تم حذف الراتب بنجاح")
               refresh()

     def pay_salary():
          selected = tree.selection()
          if not selected:
               messagebox.showerror("خطأ", "يرجى اختيار راتب لتسديده")
               return

          salary_id = tree.item(selected[0])["values"][0]
          salary = next((s for s in SALARIES_DATA if s["salary_id"] == salary_id), None)

          if not salary:
               messagebox.showerror("خطأ", "لم يتم العثور على بيانات الراتب المحدد")
               return

          if salary["status"] == "مدفوع":
               messagebox.showwarning("تحذير", "الراتب مسدد بالفعل")
               return

          dialog = tk.Toplevel()
          dialog.title("تسديد الراتب")
          dialog.geometry("400x300")
          center_window(dialog, 400, 300)

          tk.Label(dialog, text=f"تسديد راتب الموظف: {salary['employee']}",
                   font=FONTS["subtitle"], fg=COLORS["primary"]).pack(pady=10)

          tk.Label(dialog, text=f"المبلغ: {salary['amount']} {salary.get('currency', 'ريال')}",
                   font=FONTS["normal"]).pack(pady=5)

          tk.Label(dialog, text="طريقة الدفع:", font=FONTS["small"]).pack(pady=5)
          payment_var = tk.StringVar(value="تحويل بنكي")
          payment_combo = ttk.Combobox(dialog, textvariable=payment_var,
                                       values=["نقدي", "تحويل بنكي", "شيك", "بطاقة ائتمان"],
                                       font=FONTS["small"], state="readonly")
          payment_combo.pack(pady=5)

          tk.Label(dialog, text="تاريخ الدفع:", font=FONTS["small"]).pack(pady=5)
          date_entry = ttk.Entry(dialog, font=FONTS["small"])
          date_entry.insert(0, get_today_date())
          date_entry.pack(pady=5)

          def confirm_payment():
               salary["status"] = "مدفوع"
               salary["payment_method"] = payment_var.get()
               salary["payment_date"] = date_entry.get()
               salary["updated_at"] = f"{get_today_date()} {get_current_time()}"

               save_salaries()
               dialog.destroy()
               messagebox.showinfo("تم", "تم تسديد الراتب بنجاح")
               refresh()

          btn_frame = tk.Frame(dialog)
          btn_frame.pack(pady=20)

          tk.Button(btn_frame, text="تأكيد التسديد", font=FONTS["normal"], bg=COLORS["success"],
                    fg="white", command=confirm_payment).pack(side="right", padx=10)
          tk.Button(btn_frame, text="إلغاء", font=FONTS["normal"], bg=COLORS["danger"],
                    fg="white", command=dialog.destroy).pack(side="left", padx=10)

     def generate_payroll():
          dialog = tk.Toplevel()
          dialog.title("إنشاء كشف رواتب")
          dialog.geometry("500x300")
          center_window(dialog, 500, 300)

          tk.Label(dialog, text="إنشاء كشف رواتب شهري",
                   font=FONTS["subtitle"], fg=COLORS["primary"]).pack(pady=10)

          tk.Label(dialog, text="اختر الشهر:", font=FONTS["normal"]).pack(pady=5)
          month_var = tk.StringVar(value=datetime.datetime.now().strftime("%B"))
          month_combo = ttk.Combobox(dialog, textvariable=month_var,
                                     values=["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                                             "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"],
                                     font=FONTS["normal"], state="readonly")
          month_combo.pack(pady=5)

          tk.Label(dialog, text="اختر السنة:", font=FONTS["normal"]).pack(pady=5)
          year_var = tk.StringVar(value=datetime.datetime.now().strftime("%Y"))
          year_combo = ttk.Combobox(dialog, textvariable=year_var,
                                    values=[str(year) for year in range(2023, 2026)],
                                    font=FONTS["normal"], state="readonly")
          year_combo.pack(pady=5)

          def generate_report():
               month = month_var.get()
               year = year_combo.get()

               # تصفية الرواتب حسب الشهر والسنة والمركز
               payroll = [s for s in SALARIES_DATA
                          if s["month"] == month and s["year"] == year and s["center"] == center_name]

               if not payroll:
                    messagebox.showwarning("تحذير", f"لا يوجد رواتب لشهر {month} {year}")
                    return

               # في الواقع، هنا سيتم إنشاء ملف Excel أو PDF
               report_text = f"""
            كشف رواتب مركز {center_name}
            للشهر: {month} {year}
            ====================================
            """

               total = 0
               for salary in payroll:
                    report_text += f"""
                الموظف: {salary['employee']}
                الدور: {salary['role']}
                نوع الراتب: {salary['type']}
                المبلغ: {salary['amount']} {salary.get('currency', 'ريال')}
                الحالة: {salary['status']}
                ====================================
                """
                    total += salary['amount']

               report_text += f"""
            المجموع الكلي: {total} ريال
            عدد الموظفين: {len(payroll)}
            """

               messagebox.showinfo("كشف الرواتب", report_text)
               dialog.destroy()

          btn_frame = tk.Frame(dialog)
          btn_frame.pack(pady=20)

          tk.Button(btn_frame, text="إنشاء الكشف", font=FONTS["normal"], bg=COLORS["success"],
                    fg="white", command=generate_report).pack(side="right", padx=10)
          tk.Button(btn_frame, text="إلغاء", font=FONTS["normal"], bg=COLORS["danger"],
                    fg="white", command=dialog.destroy).pack(side="left", padx=10)

     def export_to_excel():
          file_path = filedialog.asksaveasfilename(
               defaultextension=".xlsx",
               filetypes=[("Excel Files", "*.xlsx"), ("All Files", "*.*")],
               title="حفظ كملف Excel"
          )

          if not file_path:
               return

          try:
               # إنشاء بيانات Excel (في الواقع سيتم استخدام مكتبة مثل openpyxl)
               # هذا مثال مبسط للتوضيح فقط
               excel_data = "رقم الراتب,اسم الموظف,الدور,نوع الراتب,المبلغ,العملة,الشهر,السنة,حالة الدفع\n"
               for salary in SALARIES_DATA:
                    if salary["center"] == center_name:
                         excel_data += f"{salary['salary_id']},{salary['employee']},{salary['role']},{salary['type']},{salary['amount']},{salary.get('currency', 'ريال')},{salary['month']},{salary['year']},{salary['status']}\n"

               with open(file_path, "w", encoding="utf-8") as f:
                    f.write(excel_data)

               messagebox.showinfo("تم", f"تم تصدير البيانات إلى {file_path}")
          except Exception as e:
               messagebox.showerror("خطأ", f"حدث خطأ أثناء التصدير: {str(e)}")

     # أزرار التحكم
     tk.Button(toolbar, text="إضافة راتب", font=FONTS["small"], bg=COLORS["success"],
               fg="white", command=add_salary).pack(side="left", padx=10)
     tk.Button(toolbar, text="تحديث", font=FONTS["small"], bg=COLORS["info"],
               fg="white", command=refresh).pack(side="left", padx=10)
     tk.Button(toolbar, text="تصدير Excel", font=FONTS["small"], bg=COLORS["primary"],
               fg="white", command=export_to_excel).pack(side="left", padx=10)

     # إطار البحث والتصفية
     filter_frame = tk.Frame(tab, bg="white", padx=10, pady=10)
     filter_frame.pack(fill="x")

     tk.Label(filter_frame, text="بحث:", font=FONTS["small"], bg="white").pack(side="right")
     search_var = tk.StringVar()
     search_entry = tk.Entry(filter_frame, textvariable=search_var, font=FONTS["small"], width=30)
     search_entry.pack(side="right", padx=5)

     tk.Label(filter_frame, text="تصفية حسب الشهر:", font=FONTS["small"], bg="white").pack(side="right", padx=10)
     month_var = tk.StringVar()
     month_combo = ttk.Combobox(filter_frame, textvariable=month_var,
                                values=["الكل"] + ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                                                   "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"],
                                font=FONTS["small"], state="readonly", width=15)
     month_combo.current(0)
     month_combo.pack(side="right")

     tk.Label(filter_frame, text="تصفية حسب الحالة:", font=FONTS["small"], bg="white").pack(side="right", padx=10)
     status_var = tk.StringVar()
     status_combo = ttk.Combobox(filter_frame, textvariable=status_var,
                                 values=["الكل", "مدفوع", "غير مدفوع", "جزئي"],
                                 font=FONTS["small"], state="readonly", width=15)
     status_combo.current(0)
     status_combo.pack(side="right")

     # جدول الرواتب
     tree_frame = tk.Frame(tab)
     tree_frame.pack(fill="both", expand=True, padx=10, pady=10)

     cols = ("رقم الراتب", "اسم الموظف", "الدور", "نوع الراتب", "المبلغ", "العملة",
             "الشهر", "السنة", "حالة الدفع", "طريقة الدفع", "تاريخ الدفع", "ملاحظات")
     tree = ttk.Treeview(tree_frame, columns=cols, show="headings", style="Custom.Treeview")

     # تحديد عرض الأعمدة
     col_widths = {
          "رقم الراتب": 100,
          "اسم الموظف": 150,
          "الدور": 120,
          "نوع الراتب": 120,
          "المبلغ": 100,
          "العملة": 80,
          "الشهر": 100,
          "السنة": 80,
          "حالة الدفع": 100,
          "طريقة الدفع": 120,
          "تاريخ الدفع": 120,
          "ملاحظات": 200
     }

     for col in cols:
          tree.heading(col, text=col)
          tree.column(col, width=col_widths.get(col, 120), anchor="center")

     scroll = ttk.Scrollbar(tree_frame, orient="vertical", command=tree.yview)
     tree.configure(yscrollcommand=scroll.set)
     scroll.pack(side="left", fill="y")
     tree.pack(side="right", fill="both", expand=True)

     # عرض بيانات الرواتب
     for salary in SALARIES_DATA:
          if salary["center"] == center_name:
               tree.insert("", "end", values=(
                    salary["salary_id"],
                    salary["employee"],
                    salary["role"],
                    salary["type"],
                    salary["amount"],
                    salary.get("currency", "ريال"),
                    salary["month"],
                    salary["year"],
                    salary["status"],
                    salary.get("payment_method", ""),
                    salary.get("payment_date", ""),
                    salary.get("notes", "")
               ))

     def search_salaries(*args):
          search_text = search_var.get().lower()
          month_filter = month_var.get()
          status_filter = status_var.get()

          for item in tree.get_children():
               tree.delete(item)

          for salary in SALARIES_DATA:
               if salary["center"] == center_name:
                    if (search_text in salary["employee"].lower() or
                        search_text in salary["role"].lower() or
                        search_text in salary["salary_id"].lower()) and \
                             (month_filter == "الكل" or salary["month"] == month_filter) and \
                             (status_filter == "الكل" or salary["status"] == status_filter):
                         tree.insert("", "end", values=(
                              salary["salary_id"],
                              salary["employee"],
                              salary["role"],
                              salary["type"],
                              salary["amount"],
                              salary.get("currency", "ريال"),
                              salary["month"],
                              salary["year"],
                              salary["status"],
                              salary.get("payment_method", ""),
                              salary.get("payment_date", ""),
                              salary.get("notes", "")
                         ))

     search_var.trace("w", search_salaries)
     month_var.trace("w", search_salaries)
     status_var.trace("w", search_salaries)

     # أزرار التحكم السفلية
     control_frame = tk.Frame(tab)
     control_frame.pack(fill="x", pady=10)

     tk.Button(control_frame, text="تسديد الراتب", font=FONTS["small"], bg=COLORS["success"],
               fg="white", command=pay_salary).pack(side="right", padx=10)
     tk.Button(control_frame, text="إنشاء كشف رواتب", font=FONTS["small"], bg=COLORS["primary"],
               fg="white", command=generate_payroll).pack(side="right", padx=10)
     tk.Button(control_frame, text="تعديل الراتب", font=FONTS["small"], bg=COLORS["warning"],
               fg="white", command=edit_salary).pack(side="right", padx=10)
     tk.Button(control_frame, text="حذف الراتب", font=FONTS["small"], bg=COLORS["danger"],
               fg="white", command=delete_salary).pack(side="right", padx=10)

     # إحصائيات سريعة
     stats_frame = tk.Frame(tab, bg=COLORS["light"], pady=5)
     stats_frame.pack(fill="x")

     current_month = datetime.datetime.now().strftime("%B")
     current_year = datetime.datetime.now().strftime("%Y")

     total_salaries = sum(s["amount"] for s in SALARIES_DATA
                          if s["center"] == center_name and
                          s["month"] == current_month and
                          s["year"] == current_year)

     paid_salaries = sum(s["amount"] for s in SALARIES_DATA
                         if s["center"] == center_name and
                         s["month"] == current_month and
                         s["year"] == current_year and
                         s["status"] == "مدفوع")

     unpaid_salaries = total_salaries - paid_salaries

     tk.Label(stats_frame,
              text=f"رواتب الشهر الحالي: {total_salaries} ريال | مدفوعة: {paid_salaries} ريال | غير مدفوعة: {unpaid_salaries} ريال",
              font=FONTS["small"], bg=COLORS["light"]).pack(side="right", padx=10)

def show_center_reports_tab(tab, center_name):
     tab.pack_propagate(False)

     # شريط الأدوات مع إضافة زر تحديث
     toolbar = tk.Frame(tab, bg=COLORS["light"], pady=5)
     toolbar.pack(fill="x")

     tk.Label(toolbar, text=f"تقارير المركز - {center_name}",
              font=FONTS["subtitle"], bg=COLORS["light"]).pack(side="right", padx=10)

     def refresh_data():
          for widget in tab.winfo_children():
               widget.destroy()
          show_center_reports_tab(tab, center_name)

     tk.Button(toolbar, text="تحديث البيانات", font=FONTS["small"], bg=COLORS["info"],
               fg="white", command=refresh_data).pack(side="left", padx=10)

     # تبويبات التقارير مع تحسين المظهر
     style = ttk.Style()
     style.configure("Report.TNotebook", tabposition="n", padding=[20, 5])
     style.configure("Report.TNotebook.Tab", font=FONTS["subtitle"], padding=[10, 5])

     report_nb = ttk.Notebook(tab, style="Report.TNotebook")
     report_nb.pack(fill="both", expand=True)

     # تبويب التقارير المالية مع تحسينات
     financial_frame = tk.Frame(report_nb)
     report_nb.add(financial_frame, text="التقارير المالية")

     # إطار الإحصائيات المالية مع تصميم أفضل
     stats_frame = tk.Frame(financial_frame, bg=COLORS["light"], padx=10, pady=10)
     stats_frame.pack(fill="x", pady=10)

     # الحصول على بيانات مالية حقيقية من قاعدة البيانات
     students = get_all_students_in_center(center_name)
     paid_students = sum(1 for s in students if any(fee.get("status") == "مدفوع" for fee in s.get("fees", [])))
     unpaid_students = len(students) - paid_students

     # حساب إجمالي الرسوم والرواتب (بيانات افتراضية في هذا المثال)
     total_fees = sum(float(fee.get("amount", "0").split()[0])
                      for s in students for fee in s.get("fees", []))
     total_salaries = sum(float(teacher.get("salary", 0))
                          for teacher in get_teachers_in_center(center_name))
     net_income = total_fees - total_salaries

     financial_stats = [
          ("إجمالي الرسوم الشهرية", f"{total_fees:,.2f} ريال", COLORS["primary"]),
          ("إجمالي الرواتب الشهرية", f"{total_salaries:,.2f} ريال", COLORS["secondary"]),
          ("صافي الدخل", f"{net_income:,.2f} ريال", COLORS["success"] if net_income >= 0 else COLORS["danger"]),
          ("عدد الطلاب المسددين", f"{paid_students} طالب", COLORS["success"]),
          ("عدد الطلاب المتأخرين", f"{unpaid_students} طالب", COLORS["warning"])
     ]

     for i, (stat, value, color) in enumerate(financial_stats):
          stat_frame = tk.Frame(stats_frame, bg=color, bd=2, relief="ridge", width=200, height=60)
          stat_frame.grid(row=0, column=i, padx=5, pady=5, sticky="nsew")
          stat_frame.grid_propagate(False)

          tk.Label(stat_frame, text=stat, font=FONTS["small"], bg=color, fg="white").pack(pady=(5, 0))
          tk.Label(stat_frame, text=value, font=("Cairo", 14, "bold"), bg=color, fg="white").pack()

     # إطار الرسوم البيانية مع إمكانية التصدير
     chart_frame = tk.Frame(financial_frame, bg="white", padx=10, pady=10)
     chart_frame.pack(fill="both", expand=True)

     # إنشاء إطار للرسوم البيانية (يمكن استبدالها بمكتبة matplotlib لاحقاً)
     charts_container = tk.Frame(chart_frame, bg="white")
     charts_container.pack(fill="both", expand=True)

     # رسم بياني مبسط للإيرادات والمصروفات
     tk.Label(charts_container, text="مخطط الإيرادات والمصروفات",
              font=FONTS["subtitle"], bg="white").pack(pady=5)

     # رسم بياني مبسط باستخدام تيكانفاس (بديل مؤقت حتى إضافة matplotlib)
     canvas = tk.Canvas(charts_container, width=600, height=300, bg="white", highlightthickness=0)
     canvas.pack(pady=10)

     # رسم أعمدة بيانية مبسطة
     max_value = max(total_fees, total_salaries, abs(net_income)) or 1
     bar_height = lambda val: (val / max_value) * 250

     # رسم عمود الإيرادات
     canvas.create_rectangle(100, 300 - bar_height(total_fees), 200, 300,
                             fill=COLORS["success"], outline="")
     canvas.create_text(150, 280 - bar_height(total_fees),
                        text=f"{total_fees:,.0f}", font=FONTS["small"])
     canvas.create_text(150, 310, text="الإيرادات", font=FONTS["small"])

     # رسم عمود المصروفات
     canvas.create_rectangle(250, 300 - bar_height(total_salaries), 350, 300,
                             fill=COLORS["danger"], outline="")
     canvas.create_text(300, 280 - bar_height(total_salaries),
                        text=f"{total_salaries:,.0f}", font=FONTS["small"])
     canvas.create_text(300, 310, text="المصروفات", font=FONTS["small"])

     # رسم عمود صافي الدخل
     canvas.create_rectangle(400, 300 - bar_height(abs(net_income)), 500, 300,
                             fill=COLORS["primary"] if net_income >= 0 else COLORS["warning"], outline="")
     canvas.create_text(450, 280 - bar_height(abs(net_income)),
                        text=f"{net_income:,.0f}", font=FONTS["small"])
     canvas.create_text(450, 310, text="صافي الدخل", font=FONTS["small"])

     # تبويب التقرير اليومي مع تحسينات
     daily_frame = tk.Frame(report_nb)
     report_nb.add(daily_frame, text="التقرير اليومي")

     header_frame = tk.Frame(daily_frame, bg=COLORS["light"])
     header_frame.pack(fill="x", pady=5)
     tk.Label(header_frame, text=f"تقرير أداء مركز {center_name} ليوم {get_today_date()}",
              font=FONTS["subtitle"], bg=COLORS["light"]).pack(pady=5)

     # إضافة خيارات تصفية التاريخ
     filter_frame = tk.Frame(daily_frame, bg="white")
     filter_frame.pack(fill="x", padx=10, pady=5)

     tk.Label(filter_frame, text="اختر تاريخ:", font=FONTS["small"], bg="white").pack(side="right")
     date_var = tk.StringVar(value=get_today_date())
     date_entry = ttk.Entry(filter_frame, textvariable=date_var, font=FONTS["small"], width=15)
     date_entry.pack(side="right", padx=5)

     def filter_by_date():
          selected_date = date_var.get()
          for item in tree.get_children():
               tree.delete(item)

          teachers = get_teachers_in_center(center_name)
          for t in teachers:
               students = t["students"]
               today_rec = sum(len([r for r in s["recitations"] if r["date"] == selected_date]) for s in students)
               avg_score = "ممتاز" if len(students) > 0 and sum(
                    1 for s in students if s.get("performance", "") in ["ممتاز", "جيد جدًا"]) / len(
                    students) > 0.7 else "جيد"
               tree.insert("", "end", values=(t["username"], len(students), today_rec, avg_score))

     tk.Button(filter_frame, text="تصفية", font=FONTS["small"], bg=COLORS["info"],
               fg="white", command=filter_by_date).pack(side="right", padx=5)

     cols = ("المعلم", "عدد الطلاب", "عدد التسميعات", "متوسط التقييم")
     tree = ttk.Treeview(daily_frame, columns=cols, show="headings", style="Custom.Treeview", height=10)

     for col in cols:
          tree.heading(col, text=col)
          tree.column(col, width=150, anchor="center")

     scroll = ttk.Scrollbar(daily_frame, orient="vertical", command=tree.yview)
     tree.configure(yscrollcommand=scroll.set)
     scroll.pack(side="left", fill="y")
     tree.pack(side="right", fill="both", expand=True)

     teachers = get_teachers_in_center(center_name)
     for t in teachers:
          students = t["students"]
          today_rec = sum(len([r for r in s["recitations"] if r["date"] == get_today_date()]) for s in students)
          avg_score = "ممتاز" if len(students) > 0 and sum(
               1 for s in students if s.get("performance", "") in ["ممتاز", "جيد جدًا"]) / len(students) > 0.7 else "جيد"
          tree.insert("", "end", values=(t["username"], len(students), today_rec, avg_score))

     # تبويب التقرير الشهري مع تحسينات
     monthly_frame = tk.Frame(report_nb)
     report_nb.add(monthly_frame, text="التقرير الشهري")

     monthly_header = tk.Frame(monthly_frame, bg=COLORS["light"])
     monthly_header.pack(fill="x", pady=5)
     tk.Label(monthly_header, text=f"تقرير أداء مركز {center_name} للشهر الحالي",
              font=FONTS["subtitle"], bg=COLORS["light"]).pack(pady=5)

     # إضافة خيارات تصفية الشهر والسنة
     month_filter_frame = tk.Frame(monthly_frame, bg="white")
     month_filter_frame.pack(fill="x", padx=10, pady=5)

     months = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
               "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"]
     current_month = datetime.datetime.now().strftime("%m")

     tk.Label(month_filter_frame, text="اختر الشهر:", font=FONTS["small"], bg="white").pack(side="right")
     month_var = tk.StringVar(value=months[int(current_month) - 1])
     month_combo = ttk.Combobox(month_filter_frame, textvariable=month_var,
                                values=months, font=FONTS["small"], state="readonly", width=12)
     month_combo.pack(side="right", padx=5)

     tk.Label(month_filter_frame, text="اختر السنة:", font=FONTS["small"], bg="white").pack(side="right")
     year_var = tk.StringVar(value=datetime.datetime.now().strftime("%Y"))
     year_combo = ttk.Combobox(month_filter_frame, textvariable=year_var,
                               values=[str(y) for y in range(2020, 2030)],
                               font=FONTS["small"], state="readonly", width=8)
     year_combo.pack(side="right", padx=5)

     def filter_by_month():
          selected_month = months.index(month_var.get()) + 1
          selected_year = year_var.get()
          month_str = f"{selected_year}-{selected_month:02d}"

          for item in monthly_tree.get_children():
               monthly_tree.delete(item)

          teachers = get_teachers_in_center(center_name)
          for t in teachers:
               students = t["students"]
               total_rec = sum(len([r for r in s["recitations"] if r["date"].startswith(month_str)]) for s in students)
               excellent = sum(1 for s in students if s.get("performance", "") in ["ممتاز", "جيد جدًا"])
               need_help = sum(1 for s in students if s.get("performance", "") in ["مقبول", "ضعيف"])
               monthly_tree.insert("", "end", values=(t["username"], len(students), total_rec, excellent, need_help))

     tk.Button(month_filter_frame, text="تصفية", font=FONTS["small"], bg=COLORS["info"],
               fg="white", command=filter_by_month).pack(side="right", padx=5)

     cols = ("المعلم", "عدد الطلاب", "إجمالي التسميعات", "طلاب ممتازين", "طلاب يحتاجون متابعة")
     monthly_tree = ttk.Treeview(monthly_frame, columns=cols, show="headings", style="Custom.Treeview", height=10)

     for col in cols:
          monthly_tree.heading(col, text=col)
          monthly_tree.column(col, width=120, anchor="center")

     scroll = ttk.Scrollbar(monthly_frame, orient="vertical", command=monthly_tree.yview)
     monthly_tree.configure(yscrollcommand=scroll.set)
     scroll.pack(side="left", fill="y")
     monthly_tree.pack(side="right", fill="both", expand=True)

     current_month = datetime.datetime.now().strftime("%Y-%m")
     for t in teachers:
          students = t["students"]
          total_rec = sum(len([r for r in s["recitations"] if r["date"].startswith(current_month)]) for s in students)
          excellent = sum(1 for s in students if s.get("performance", "") in ["ممتاز", "جيد جدًا"])
          need_help = sum(1 for s in students if s.get("performance", "") in ["مقبول", "ضعيف"])
          monthly_tree.insert("", "end", values=(t["username"], len(students), total_rec, excellent, need_help))

     # تبويب جديد لتقارير الطلاب
     students_frame = tk.Frame(report_nb)
     report_nb.add(students_frame, text="تقارير الطلاب")

     students_header = tk.Frame(students_frame, bg=COLORS["light"])
     students_header.pack(fill="x", pady=5)
     tk.Label(students_header, text=f"تقارير أداء الطلاب في مركز {center_name}",
              font=FONTS["subtitle"], bg=COLORS["light"]).pack(pady=5)

     # إضافة خيارات تصفية حسب المعلم
     student_filter_frame = tk.Frame(students_frame, bg="white")
     student_filter_frame.pack(fill="x", padx=10, pady=5)

     tk.Label(student_filter_frame, text="اختر المعلم:", font=FONTS["small"], bg="white").pack(side="right")
     teacher_var = tk.StringVar(value="الكل")
     teacher_combo = ttk.Combobox(student_filter_frame, textvariable=teacher_var,
                                  values=["الكل"] + [t["username"] for t in teachers],
                                  font=FONTS["small"], state="readonly", width=15)
     teacher_combo.pack(side="right", padx=5)

     def filter_students():
          selected_teacher = teacher_var.get()

          for item in students_tree.get_children():
               students_tree.delete(item)

          students = get_all_students_in_center(center_name)
          for s in students:
               if selected_teacher == "الكل" or s["teacher"] == selected_teacher:
                    last_rec = s["recitations"][-1] if s["recitations"] else {}
                    students_tree.insert("", "end", values=(
                         s.get("student_id", ""),
                         s["name"],
                         s["teacher"],
                         s["level"],
                         s["current_surah"],
                         s["performance"],
                         last_rec.get("date", "لا يوجد"),
                         last_rec.get("score", "لا يوجد")
                    ))

     tk.Button(student_filter_frame, text="تصفية", font=FONTS["small"], bg=COLORS["info"],
               fg="white", command=filter_students).pack(side="right", padx=5)

     cols = ("رقم الطالب", "الاسم", "المعلم", "المستوى", "السورة الحالية", "الأداء", "آخر تسميع", "تقييم آخر تسميع")
     students_tree = ttk.Treeview(students_frame, columns=cols, show="headings", style="Custom.Treeview", height=15)

     for col in cols:
          students_tree.heading(col, text=col)
          students_tree.column(col, width=120, anchor="center")

     scroll = ttk.Scrollbar(students_frame, orient="vertical", command=students_tree.yview)
     students_tree.configure(yscrollcommand=scroll.set)
     scroll.pack(side="left", fill="y")
     students_tree.pack(side="right", fill="both", expand=True)

     students = get_all_students_in_center(center_name)
     for s in students:
          last_rec = s["recitations"][-1] if s["recitations"] else {}
          students_tree.insert("", "end", values=(
               s.get("student_id", ""),
               s["name"],
               s["teacher"],
               s["level"],
               s["current_surah"],
               s["performance"],
               last_rec.get("date", "لا يوجد"),
               last_rec.get("score", "لا يوجد")
          ))

     # أزرار التحكم مع إضافة ميزات جديدة
     control_frame = tk.Frame(tab)
     control_frame.pack(fill="x", pady=10)

     def generate_detailed_report():
          selected_tab = report_nb.index(report_nb.select())
          report_name = report_nb.tab(selected_tab, "text")

          if report_name == "التقارير المالية":
               data = financial_stats
               title = f"تقرير مالي لمركز {center_name}"
          elif report_name == "التقرير اليومي":
               data = []
               for item in tree.get_children():
                    data.append(tree.item(item)["values"])
               title = f"تقرير يومي لمركز {center_name} بتاريخ {date_var.get()}"
          elif report_name == "التقرير الشهري":
               data = []
               for item in monthly_tree.get_children():
                    data.append(monthly_tree.item(item)["values"])
               title = f"تقرير شهري لمركز {center_name} لشهر {month_var.get()} {year_var.get()}"
          else:
               data = []
               for item in students_tree.get_children():
                    data.append(students_tree.item(item)["values"])
               title = f"تقرير الطلاب لمركز {center_name}"

          # في الواقع هنا سيتم إنشاء ملف PDF أو Excel
          messagebox.showinfo("تقرير مفصل", f"سيتم إنشاء تقرير مفصل لـ {report_name}")

     def export_to_excel():
          file_path = filedialog.asksaveasfilename(
               defaultextension=".xlsx",
               filetypes=[("Excel Files", "*.xlsx"), ("All Files", "*.*")],
               title="حفظ التقرير كملف Excel"
          )

          if file_path:
               # في الواقع هنا سيتم إنشاء ملف Excel باستخدام مكتبة مثل openpyxl
               messagebox.showinfo("تم", f"تم تصدير البيانات إلى {file_path}")

     def export_to_pdf():
          file_path = filedialog.asksaveasfilename(
               defaultextension=".pdf",
               filetypes=[("PDF Files", "*.pdf"), ("All Files", "*.*")],
               title="حفظ التقرير كملف PDF"
          )

          if file_path:
               # في الواقع هنا سيتم إنشاء ملف PDF باستخدام مكتبة مثل reportlab
               messagebox.showinfo("تم", f"تم تصدير البيانات إلى {file_path}")

     tk.Button(control_frame, text="إنشاء تقرير مفصل", font=FONTS["small"], bg=COLORS["primary"],
               fg="white", command=generate_detailed_report).pack(side="right", padx=5)
     tk.Button(control_frame, text="تصدير إلى Excel", font=FONTS["small"], bg=COLORS["success"],
               fg="white", command=export_to_excel).pack(side="right", padx=5)
     tk.Button(control_frame, text="تصدير إلى PDF", font=FONTS["small"], bg=COLORS["warning"],
               fg="white", command=export_to_pdf).pack(side="right", padx=5)
     tk.Button(control_frame, text="طباعة التقرير", font=FONTS["small"], bg=COLORS["info"],
               fg="white", command=lambda: messagebox.showinfo("طباعة", "سيتم طباعة التقرير الحالي")).pack(side="right",
                                                                                                           padx=5)
# ----- لوحة المعلم مع تحسينات -----
def teacher_dashboard(username):
     # الحصول على بيانات المعلم والمركز
     teacher_data = get_teacher_data(username)
     if not teacher_data:
          messagebox.showerror("خطأ", "لم يتم العثور على بيانات المعلم")
          return

     center_name = teacher_data.get("center", "")
     if not center_name:
          messagebox.showerror("خطأ", "لم يتم تعيين مركز للمعلم")
          return

     # إنشاء النافذة الرئيسية
     root = tk.Tk()
     root.title(f"لوحة المعلم - {SYSTEM_NAME} | {center_name}")
     root.geometry("1200x800")  # زيادة الحجم لتحسين المساحة
     center_window(root, 1200, 800)

     # تحميل أيقونة البرنامج إذا وجدت
     try:
          root.iconbitmap("assets/icon.ico")
     except:
          pass

     # ========== شريط الأدوات العلوي مع تحسينات ==========
     toolbar = tk.Frame(root, bg=COLORS["primary"], height=60)
     toolbar.pack(fill="x")

     # إضافة شعار المركز إذا وجد
     try:
          logo_img = Image.open(f"centers/{center_name}/logo.png").resize((40, 40))
          logo_photo = ImageTk.PhotoImage(logo_img)
          logo_label = tk.Label(toolbar, image=logo_photo, bg=COLORS["primary"])
          logo_label.image = logo_photo
          logo_label.pack(side="right", padx=10)
     except:
          pass

     # معلومات المعلم مع صورة البروفايل
     user_frame = tk.Frame(toolbar, bg=COLORS["primary"])
     user_frame.pack(side="right", padx=20)

     try:
          profile_img = Image.open(f"users/{username}/profile.png").resize((30, 30))
          profile_photo = ImageTk.PhotoImage(profile_img)
          profile_label = tk.Label(user_frame, image=profile_photo, bg=COLORS["primary"])
          profile_label.image = profile_photo
          profile_label.pack(side="right")
     except:
          pass

     tk.Label(user_frame, text=f"مرحباً بك، {teacher_data.get('full_name', username)}",
              font=FONTS["subtitle"], bg=COLORS["primary"], fg="white").pack(side="right", padx=10)

     # أزرار التحكم في الشريط العلوي
     btn_frame = tk.Frame(toolbar, bg=COLORS["primary"])
     btn_frame.pack(side="left", padx=10)

     def logout():
          if messagebox.askyesno("تأكيد", "هل أنت متأكد من تسجيل الخروج؟"):
               root.destroy()
               login_screen()

     def refresh_data():
          load_data()
          messagebox.showinfo("تم", "تم تحديث البيانات بنجاح")
          # إعادة تحميل التبويبات
          show_teacher_main(tabs["الرئيسية"], teacher_data)
          show_teacher_students_tab(tabs["طلابي"], teacher_data)
          show_teacher_recitations_tab(tabs["التسميعات"], teacher_data)
          show_teacher_attendance_tab(tabs["الحضور"], teacher_data)

     def show_notifications():
          notifications_window = tk.Toplevel(root)
          notifications_window.title("الإشعارات والتنبيهات")
          notifications_window.geometry("500x400")
          center_window(notifications_window, 500, 400)

          # إشعارات افتراضية (يمكن جلبها من قاعدة البيانات)
          notifications = [
               f"لديك {len(teacher_data.get('students', []))} طلاب مسجلين",
               f"لديك {sum(1 for s in teacher_data.get('students', []) if s.get('fees_status') == 'غير مدفوع')} طلاب متأخرين في السداد",
               "التسميعات اليوم: " + str(sum(len([r for r in s.get('recitations', [])
                                                  if r.get('date') == get_today_date()])
                                             for s in teacher_data.get('students', [])))
          ]

          tk.Label(notifications_window, text="الإشعارات الحديثة",
                   font=FONTS["subtitle"], fg=COLORS["primary"]).pack(pady=10)

          for note in notifications:
               tk.Label(notifications_window, text=f"• {note}",
                        font=FONTS["normal"], wraplength=450, justify="right").pack(pady=5, padx=10, anchor="w")

     # أزرار الشريط العلوي
     tk.Button(btn_frame, text="تسجيل الخروج", font=FONTS["small"], bg=COLORS["danger"],
               fg="white", command=logout).pack(side="left", padx=5)
     tk.Button(btn_frame, text="تحديث البيانات", font=FONTS["small"], bg=COLORS["info"],
               fg="white", command=refresh_data).pack(side="left", padx=5)
     tk.Button(btn_frame, text="الإشعارات", font=FONTS["small"], bg=COLORS["secondary"],
               fg="white", command=show_notifications).pack(side="left", padx=5)

     # ========== تبويبات التحكم مع تحسينات ==========
     style = ttk.Style()
     style.configure("Teacher.TNotebook", tabposition="n", padding=[30, 5])
     style.configure("Teacher.TNotebook.Tab",
                     font=FONTS["subtitle"],
                     padding=[15, 10],
                     background=COLORS["light"],
                     foreground=COLORS["dark"])

     nb = ttk.Notebook(root, style="Teacher.TNotebook")
     nb.pack(fill="both", expand=True, padx=10, pady=5)

     # إنشاء التبويبات مع أيقونات
     tabs = {
          "الرئيسية": {"frame": tk.Frame(nb, bg="#f8f9fa"), "icon": "🏠"},
          "طلابي": {"frame": tk.Frame(nb), "icon": "🧑‍🎓"},
          "التسميعات": {"frame": tk.Frame(nb), "icon": "📖"},
          "الحضور": {"frame": tk.Frame(nb), "icon": "✅"},
          "التقارير": {"frame": tk.Frame(nb), "icon": "📊"}
     }

     for name, tab_data in tabs.items():
          nb.add(tab_data["frame"], text=f"{tab_data['icon']} {name}")

     # تعبئة التبويبات بالمحتوى مع معالجة الأخطاء
     try:
          show_teacher_main(tabs["الرئيسية"]["frame"], teacher_data)
          show_teacher_students_tab(tabs["طلابي"]["frame"], teacher_data)
          show_teacher_recitations_tab(tabs["التسميعات"]["frame"], teacher_data)
          show_teacher_attendance_tab(tabs["الحضور"]["frame"], teacher_data)
          show_teacher_reports_tab(tabs["التقارير"]["frame"], teacher_data)
     except Exception as e:
          messagebox.showerror("خطأ", f"حدث خطأ أثناء تحميل البيانات: {str(e)}")
          root.destroy()
          return

     # ========== شريط الحالة السفلي مع تحسينات ==========
     status_bar = tk.Frame(root, bg=COLORS["dark"], height=30)
     status_bar.pack(fill="x", side="bottom")
     status_labels = tk.Frame(status_bar, bg=COLORS["dark"])
     status_labels.pack(side="right", padx=10)

     # عرض إحصائيات سريعة
     students_count = len(teacher_data.get("students", []))
     today_recitations = sum(len([r for r in s.get("recitations", [])
                                  if r.get("date") == get_today_date()])
                             for s in teacher_data.get("students", []))
     attendance_percentage = "0%"
     if students_count > 0:
          today_attendance = sum(1 for s in teacher_data.get("students", [])
                                 for a in s.get("attendance", [])
                                 if a.get("date") == get_today_date() and a.get("status") == "حاضر")
          attendance_percentage = f"{round((today_attendance / students_count) * 100)}%"

     tk.Label(status_labels,
              text=f"عدد الطلاب: {students_count} | تسميعات اليوم: {today_recitations} | الحضور: {attendance_percentage}",
              font=FONTS["small"], bg=COLORS["dark"], fg="white").pack(side="right", padx=5)

     tk.Label(status_labels,
              text=f"{SYSTEM_NAME} | الإصدار {VERSION} | {get_today_date()} | {get_current_time()}",
              font=FONTS["small"], bg=COLORS["dark"], fg="white").pack(side="right", padx=5)

     # تحديث تلقائي للبيانات كل 5 دقائق
     def auto_refresh():
          try:
               load_data()  # إعادة تحميل البيانات من الملفات
               root.after(300000, auto_refresh)  # 5 دقائق = 300000 مللي ثانية
          except:
               pass

     root.after(300000, auto_refresh)

     root.mainloop()

def show_teacher_main(tab, teacher_data):
     tab.pack_propagate(False)

     # تنظيف المحتوى السابق إذا وجد
     for widget in tab.winfo_children():
          widget.destroy()

     # ========== إطار العنوان مع تحسينات ==========
     header = tk.Frame(tab, bg=COLORS["primary"], height=80)
     header.pack(fill="x", pady=(0, 10))

     tk.Label(header, text=f"نظرة عامة - {teacher_data.get('full_name', '')}",
              font=FONTS["title"], bg=COLORS["primary"], fg="white").pack(pady=20)

     # ========== إطار الإحصائيات مع تحسينات ==========
     stats_container = tk.Frame(tab, bg="#f0f0f0", padx=10, pady=10)
     stats_container.pack(fill="x", pady=(0, 20))

     students = teacher_data.get("students", [])
     today = get_today_date()
     # عند معالجة سجلات التسميع
     for student in teacher_data.get("students", []):
          for rec in student.get("recitations", []):
               try:
                    rec_date = datetime.datetime.strptime(rec.get("date", "2000-01-01"), "%Y-%m-%d")
               except ValueError:
                    # إذا كان التاريخ غير صالح، استخدم تاريخ افتراضي
                    rec_date = datetime.datetime.strptime("2000-01-01", "%Y-%m-%d")

     # حساب التسميعات والحضور
     today_recitations = sum(len([r for r in s.get("recitations", [])
                                  if r.get("date") == today])
                             for s in students)

     today_attendance = sum(1 for s in students
                            for a in s.get("attendance", [])
                            if a.get("date") == today and a.get("status") == "حاضر")

     attendance_percentage = "0%"
     if students:
          attendance_percentage = f"{round((today_attendance / len(students)) * 100)}%"

     stats = [
          {"label": "عدد الطلاب", "value": len(students), "color": COLORS["primary"], "icon": "🧑‍🎓"},
          {"label": "تسميعات اليوم", "value": today_recitations, "color": COLORS["secondary"], "icon": "📖"},
          {"label": "الحضور اليوم", "value": attendance_percentage, "color": COLORS["success"], "icon": "✅"},
          {"label": "طلاب ممتازين", "value": sum(1 for s in students if s.get("performance") == "ممتاز"),
           "color": COLORS["info"], "icon": "⭐"},
          {"label": "متأخرون في السداد", "value": sum(1 for s in students if s.get("fees_status") == "غير مدفوع"),
           "color": COLORS["warning"], "icon": "⚠️"},
          {"label": "متوسط التسميع/طالب",
           "value": round(len([r for s in students for r in s.get("recitations", [])]) / max(1, len(students))),
           "color": COLORS["accent"], "icon": "📊"}
     ]

     for i, stat in enumerate(stats):
          stat_frame = tk.Frame(stats_container, bg=stat["color"], bd=2, relief="ridge",
                                width=200, height=120)
          stat_frame.grid(row=i // 3, column=i % 3, padx=10, pady=10, sticky="nsew")
          stat_frame.grid_propagate(False)

          # إضافة أيقونة
          tk.Label(stat_frame, text=stat["icon"], font=("Arial", 20),
                   bg=stat["color"], fg="white").pack(pady=(10, 0))

          # إضافة التسمية
          tk.Label(stat_frame, text=stat["label"], font=FONTS["small"],
                   bg=stat["color"], fg="white").pack()

          # إضافة القيمة
          tk.Label(stat_frame, text=str(stat["value"]), font=("Cairo", 24, "bold"),
                   bg=stat["color"], fg="white").pack(pady=(5, 10))

     # ========== أحدث التسميعات ==========
     recitations_frame = tk.Frame(tab, bg="white", bd=1, relief="solid")
     recitations_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))

     tk.Label(recitations_frame, text="أحدث التسميعات",
              font=FONTS["subtitle"], bg="white").pack(pady=10)

     # إنشاء جدول لأحدث التسميعات
     cols = ("الطالب", "التاريخ", "السورة", "التقييم", "الأخطاء")
     tree = ttk.Treeview(recitations_frame, columns=cols, show="headings", height=5)

     for col in cols:
          tree.heading(col, text=col)
          tree.column(col, width=120, anchor="center")

     scroll = ttk.Scrollbar(recitations_frame, orient="vertical", command=tree.yview)
     tree.configure(yscrollcommand=scroll.set)
     scroll.pack(side="left", fill="y")
     tree.pack(side="right", fill="both", expand=True)

     # جمع أحدث 10 تسميعات
     all_recitations = []
     for student in students:
          for rec in student.get("recitations", []):
               all_recitations.append({
                    "student": student["name"],
                    "date": rec.get("date", ""),
                    "surah": rec.get("surah", ""),
                    "score": rec.get("score", ""),
                    "mistakes": rec.get("mistakes", ""),
                    "timestamp": datetime.datetime.strptime(rec.get("date", "2000-01-01"), "%Y-%m-%d")

               })

     # ترتيب التسميعات من الأحدث إلى الأقدم
     all_recitations.sort(key=lambda x: x["timestamp"], reverse=True)

     # إضافة أحدث 10 تسميعات إلى الجدول
     for rec in all_recitations[:10]:
          tree.insert("", "end", values=(
               rec["student"],
               rec["date"],
               rec["surah"],
               rec["score"],
               rec["mistakes"]
          ))

     # زر عرض الكل
     def show_all_recitations():
          # يمكن فتح نافذة جديدة لعرض جميع التسميعات
          messagebox.showinfo("التسميعات", "سيتم عرض جميع التسميعات هنا")

     tk.Button(recitations_frame, text="عرض الكل", font=FONTS["small"],
               bg=COLORS["primary"], fg="white", command=show_all_recitations).pack(pady=10)

def show_teacher_students_tab(tab, teacher_data):
     tab.pack_propagate(False)
     # تنظيف المحتوى السابق إذا وجد
     for widget in tab.winfo_children():
          widget.destroy()

     # ========== شريط الأدوات مع تحسينات ==========
     toolbar = tk.Frame(tab, bg=COLORS["light"], pady=5)
     toolbar.pack(fill="x")

     # العنوان مع أيقونة
     title_frame = tk.Frame(toolbar, bg=COLORS["light"])
     title_frame.pack(side="right", padx=10)
     tk.Label(title_frame, text="🧑‍🎓 قائمة الطلاب",
              font=FONTS["subtitle"], bg=COLORS["light"]).pack(side="right")

     # مجموعة أزرار التحكم
     btn_frame = tk.Frame(toolbar, bg=COLORS["light"])
     btn_frame.pack(side="left", padx=10)

     def refresh():
          global teacher_data  # إذا كان متغيرًا عامًا
          load_data()
          teacher_data = get_teacher_data(teacher_data["username"])  # الآن يعرف المتغير
          show_teacher_students_tab(tab, teacher_data)

     def add_new_student():
          dialog = tk.Toplevel()
          dialog.title("إضافة طالب جديد")
          dialog.geometry("700x800")
          center_window(dialog, 700, 800)

          # إطار التمرير للنموذج
          main_frame = tk.Frame(dialog)
          main_frame.pack(fill="both", expand=True)

          canvas = tk.Canvas(main_frame)
          scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
          scrollable_frame = tk.Frame(canvas)

          scrollable_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
          canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
          canvas.configure(yscrollcommand=scrollbar.set)

          scrollbar.pack(side="left", fill="y")
          canvas.pack(side="right", fill="both", expand=True)

          tk.Label(scrollable_frame, text="إضافة طالب جديد",
                   font=FONTS["subtitle"], fg=COLORS["primary"]).pack(pady=10)

          # حقول النموذج
          fields = [
               ("الاسم الكامل", "", True),
               ("تاريخ الميلاد (YYYY-MM-DD)", "", True),
               ("المستوى", ["مبتدئ", "متوسط", "متقدم"], True),
               ("السورة الحالية", "", False),
               ("الأداء العام", ["ممتاز", "جيد جدًا", "جيد", "مقبول"], False),
               ("اسم ولي الأمر", "", True),
               ("صلة القرابة", ["أب", "أم", "وصي", "أخ", "قريب"], True),
               ("هاتف ولي الأمر", "", True),
               ("عنوان ولي الأمر", "", True),
               ("بريد ولي الأمر", "", False),
               ("تاريخ الانضمام", get_today_date(), True),
               ("خطة الحفظ", ["3 أشهر", "6 أشهر", "سنة", "سنتين", "3 سنوات"], True),
               ("الفترة", ["صباحية", "مسائية"], True),
               ("ملاحظات", "", False)
          ]

          entries = []
          for label, default, required in fields:
               frame = tk.Frame(scrollable_frame)
               frame.pack(fill="x", padx=20, pady=5)

               tk.Label(frame, text=label + ("" if not required else " *"),
                        font=FONTS["small"], width=20, anchor="e").pack(side="right")

               if isinstance(default, list):
                    var = tk.StringVar()
                    combo = ttk.Combobox(frame, textvariable=var, values=default,
                                         font=FONTS["small"], state="readonly")
                    combo.pack(fill="x")
                    entries.append(var)
               else:
                    entry = ttk.Entry(frame, font=FONTS["small"])
                    entry.insert(0, default)
                    entry.pack(fill="x")
                    entries.append(entry)

          def save_student():
               # التحقق من الحقول المطلوبة
               for i, (label, default, required) in enumerate(fields):
                    if required and not entries[i].get():
                         messagebox.showerror("خطأ", f"حقل {label} مطلوب")
                         return

               # إنشاء رقم هوية الطالب
               student_id = f"STU{len(get_all_students()) + 1:03d}"

               new_student = {
                    "student_id": student_id,
                    "name": entries[0].get(),
                    "birth_date": entries[1].get(),
                    "level": entries[2].get(),
                    "current_surah": entries[3].get(),
                    "performance": entries[4].get(),
                    "parent_name": entries[5].get(),
                    "parent_relation": entries[6].get(),
                    "parent_contact": entries[7].get(),
                    "parent_address": entries[8].get(),
                    "parent_email": entries[9].get(),
                    "join_date": entries[10].get(),
                    "plan": entries[11].get(),
                    "period": entries[12].get(),
                    "notes": entries[13].get(),
                    "recitations": [],
                    "attendance": [],
                    "fees_status": "غير مدفوع"
               }

               # إضافة الطالب إلى بيانات المعلم
               if "students" not in teacher_data:
                    teacher_data["students"] = []
               teacher_data["students"].append(new_student)

               # حفظ التغييرات
               save_data()
               dialog.destroy()
               messagebox.showinfo("تم", "تم إضافة الطالب بنجاح")
               refresh()

          btn_frame = tk.Frame(scrollable_frame)
          btn_frame.pack(fill="x", pady=20)

          tk.Button(btn_frame, text="حفظ", font=FONTS["normal"], bg=COLORS["success"],
                    fg="white", command=save_student).pack(side="right", padx=10, ipadx=20)
          tk.Button(btn_frame, text="إلغاء", font=FONTS["normal"], bg=COLORS["danger"],
                    fg="white", command=dialog.destroy).pack(side="left", padx=10, ipadx=20)

     # أزرار الشريط العلوي
     tk.Button(btn_frame, text="إضافة طالب", font=FONTS["small"], bg=COLORS["success"],
               fg="white", command=add_new_student).pack(side="left", padx=5)
     tk.Button(btn_frame, text="تحديث", font=FONTS["small"], bg=COLORS["info"],
               fg="white", command=refresh).pack(side="left", padx=5)

     # ========== إطار البحث والتصفية ==========
     filter_frame = tk.Frame(tab, bg="white", padx=10, pady=10)
     filter_frame.pack(fill="x")

     tk.Label(filter_frame, text="بحث:", font=FONTS["small"], bg="white").pack(side="right")
     search_var = tk.StringVar()
     search_entry = tk.Entry(filter_frame, textvariable=search_var, font=FONTS["small"], width=30)
     search_entry.pack(side="right", padx=5)

     tk.Label(filter_frame, text="تصفية حسب المستوى:", font=FONTS["small"], bg="white").pack(side="right", padx=10)
     level_var = tk.StringVar(value="الكل")
     level_combo = ttk.Combobox(filter_frame, textvariable=level_var,
                                values=["الكل", "مبتدئ", "متوسط", "متقدم"],
                                font=FONTS["small"], state="readonly", width=10)
     level_combo.pack(side="right")

     # ========== جدول الطلاب مع تحسينات ==========
     tree_container = tk.Frame(tab)
     tree_container.pack(fill="both", expand=True, padx=10, pady=(0, 10))

     cols = ("الاسم", "المستوى", "السورة الحالية", "الأداء", "تاريخ الانضمام", "الحالة")
     tree = ttk.Treeview(tree_container, columns=cols, show="headings", style="Custom.Treeview", height=15)

     # تحديد عرض الأعمدة
     col_widths = {
          "الاسم": 150,
          "المستوى": 100,
          "السورة الحالية": 120,
          "الأداء": 100,
          "تاريخ الانضمام": 120,
          "الحالة": 80
     }

     for col in cols:
          tree.heading(col, text=col)
          tree.column(col, width=col_widths.get(col, 120), anchor="center")

     scroll = ttk.Scrollbar(tree_container, orient="vertical", command=tree.yview)
     tree.configure(yscrollcommand=scroll.set)
     scroll.pack(side="left", fill="y")
     tree.pack(side="right", fill="both", expand=True)

     # تعبئة الجدول بالبيانات
     def load_students_data():
          for item in tree.get_children():
               tree.delete(item)

          students = teacher_data.get("students", [])
          for student in students:
               tree.insert("", "end", values=(
                    student["name"],
                    student["level"],
                    student.get("current_surah", ""),
                    student.get("performance", ""),
                    student.get("join_date", ""),
                    student.get("fees_status", "غير معروف")
               ), tags=(student.get("fees_status", ""),))

          tree.tag_configure("مدفوع", background=COLORS["light"])
          tree.tag_configure("غير مدفوع", background="#ffdddd")

     load_students_data()

     # وظيفة البحث والتصفية
     def filter_students(*args):
          search_text = search_var.get().lower()
          level_filter = level_var.get()

          for item in tree.get_children():
               tree.delete(item)

          students = teacher_data.get("students", [])
          for student in students:
               if (search_text in student["name"].lower() or
                   search_text in student.get("current_surah", "").lower()) and \
                        (level_filter == "الكل" or student["level"] == level_filter):
                    tree.insert("", "end", values=(
                         student["name"],
                         student["level"],
                         student.get("current_surah", ""),
                         student.get("performance", ""),
                         student.get("join_date", ""),
                         student.get("fees_status", "غير معروف")
                    ), tags=(student.get("fees_status", ""),))

     search_var.trace("w", filter_students)
     level_var.trace("w", filter_students)

     # ========== أزرار التحكم السفلية ==========
     control_frame = tk.Frame(tab)
     control_frame.pack(fill="x", pady=10)

     def open_selected():
          selected = tree.selection()
          if not selected:
               messagebox.showerror("خطأ", "يرجى اختيار طالب")
               return

          student_name = tree.item(selected[0])["values"][0]
          student = next((s for s in teacher_data.get("students", []) if s["name"] == student_name), None)
          if student:
               open_student_file(student)
          else:
               messagebox.showerror("خطأ", "لم يتم العثور على بيانات الطالب")

     def edit_selected():
          selected = tree.selection()
          if not selected:
               messagebox.showerror("خطأ", "يرجى اختيار طالب لتعديله")
               return

          student_name = tree.item(selected[0])["values"][0]
          student = next((s for s in teacher_data.get("students", []) if s["name"] == student_name), None)
          if not student:
               messagebox.showerror("خطأ", "لم يتم العثور على بيانات الطالب")
               return

          dialog = tk.Toplevel()
          dialog.title(f"تعديل بيانات الطالب {student_name}")
          dialog.geometry("600x700")
          center_window(dialog, 600, 700)

          # إطار التمرير للنموذج
          main_frame = tk.Frame(dialog)
          main_frame.pack(fill="both", expand=True)

          canvas = tk.Canvas(main_frame)
          scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
          scrollable_frame = tk.Frame(canvas)

          scrollable_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
          canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
          canvas.configure(yscrollcommand=scrollbar.set)

          scrollbar.pack(side="left", fill="y")
          canvas.pack(side="right", fill="both", expand=True)

          tk.Label(scrollable_frame, text=f"تعديل بيانات الطالب {student_name}",
                   font=FONTS["subtitle"], fg=COLORS["primary"]).pack(pady=10)

          fields = [
               ("الاسم الكامل", student["name"], True),
               ("تاريخ الميلاد", student.get("birth_date", ""), True),
               ("المستوى", student["level"], ["مبتدئ", "متوسط", "متقدم"], True),
               ("السورة الحالية", student.get("current_surah", ""), False),
               ("الأداء العام", student.get("performance", ""), ["ممتاز", "جيد جدًا", "جيد", "مقبول"], False),
               ("اسم ولي الأمر", student.get("parent_name", ""), True),
               ("صلة القرابة", student.get("parent_relation", ""), ["أب", "أم", "وصي", "أخ", "قريب"], True),
               ("هاتف ولي الأمر", student.get("parent_contact", ""), True),
               ("عنوان ولي الأمر", student.get("parent_address", ""), True),
               ("بريد ولي الأمر", student.get("parent_email", ""), False),
               ("تاريخ الانضمام", student.get("join_date", ""), True),
               ("خطة الحفظ", student.get("plan", ""), ["3 أشهر", "6 أشهر", "سنة", "سنتين", "3 سنوات"], True),
               ("الفترة", student.get("period", ""), ["صباحية", "مسائية"], True),
               ("ملاحظات", student.get("notes", ""), False),
               ("حالة الرسوم", student.get("fees_status", "غير مدفوع"), ["مدفوع", "غير مدفوع"], True)
          ]

          entries = []
          for field in fields:
               frame = tk.Frame(scrollable_frame)
               frame.pack(fill="x", padx=20, pady=5)

               tk.Label(frame, text=field[0] + ("" if not field[-1] else " *"),
                        font=FONTS["small"], width=20, anchor="e").pack(side="right")

               if len(field) == 4:  # حقل يحتوي على قائمة اختيار
                    var = tk.StringVar(value=field[1])
                    combo = ttk.Combobox(frame, textvariable=var, values=field[2],
                                         font=FONTS["small"], state="readonly")
                    combo.pack(fill="x")
                    entries.append(var)
               else:
                    entry = ttk.Entry(frame, font=FONTS["small"])
                    entry.insert(0, field[1])
                    entry.pack(fill="x")
                    entries.append(entry)

          def update_student():
               # التحقق من الحقول المطلوبة
               required_indices = [i for i, field in enumerate(fields) if field[-1]]
               for i in required_indices:
                    if not entries[i].get():
                         messagebox.showerror("خطأ", f"حقل {fields[i][0]} مطلوب")
                         return

               # تحديث بيانات الطالب
               student.update({
                    "name": entries[0].get(),
                    "birth_date": entries[1].get(),
                    "level": entries[2].get(),
                    "current_surah": entries[3].get(),
                    "performance": entries[4].get(),
                    "parent_name": entries[5].get(),
                    "parent_relation": entries[6].get(),
                    "parent_contact": entries[7].get(),
                    "parent_address": entries[8].get(),
                    "parent_email": entries[9].get(),
                    "join_date": entries[10].get(),
                    "plan": entries[11].get(),
                    "period": entries[12].get(),
                    "notes": entries[13].get(),
                    "fees_status": entries[14].get()
               })

               save_data()
               dialog.destroy()
               messagebox.showinfo("تم", "تم تحديث بيانات الطالب بنجاح")
               refresh()

          btn_frame = tk.Frame(scrollable_frame)
          btn_frame.pack(fill="x", pady=20)

          tk.Button(btn_frame, text="حفظ التعديلات", font=FONTS["normal"], bg=COLORS["success"],
                    fg="white", command=update_student).pack(side="right", padx=10, ipadx=20)
          tk.Button(btn_frame, text="إلغاء", font=FONTS["normal"], bg=COLORS["danger"],
                    fg="white", command=dialog.destroy).pack(side="left", padx=10, ipadx=20)

     def delete_selected():
          selected = tree.selection()
          if not selected:
               messagebox.showerror("خطأ", "يرجى اختيار طالب لحذفه")
               return

          student_name = tree.item(selected[0])["values"][0]

          if messagebox.askyesno("تأكيد", f"هل أنت متأكد من حذف الطالب {student_name}؟"):
               teacher_data["students"] = [s for s in teacher_data.get("students", []) if s["name"] != student_name]
               save_data()
               messagebox.showinfo("تم", f"تم حذف الطالب {student_name} بنجاح")
               refresh()

     # أزرار التحكم
     tk.Button(control_frame, text="فتح ملف الطالب", font=FONTS["small"], bg=COLORS["primary"],
               fg="white", command=open_selected).pack(side="right", padx=10)
     tk.Button(control_frame, text="تعديل بيانات", font=FONTS["small"], bg=COLORS["warning"],
               fg="white", command=edit_selected).pack(side="right", padx=10)
     tk.Button(control_frame, text="حذف الطالب", font=FONTS["small"], bg=COLORS["danger"],
               fg="white", command=delete_selected).pack(side="right", padx=10)
     tk.Button(control_frame, text="إضافة تسميع", font=FONTS["small"], bg=COLORS["info"],
               fg="white").pack(side="right", padx=10)

def show_teacher_recitations_tab(tab, teacher_data):
     tab.pack_propagate(False)
     # تنظيف المحتوى السابق إذا وجد
     for widget in tab.winfo_children():
          widget.destroy()

     # ========== شريط الأدوات مع تحسينات ==========
     toolbar = tk.Frame(tab, bg=COLORS["light"], pady=5)
     toolbar.pack(fill="x")

     # العنوان مع أيقونة
     title_frame = tk.Frame(toolbar, bg=COLORS["light"])
     title_frame.pack(side="right", padx=10)
     tk.Label(title_frame, text="📖 سجل التسميعات والتحفيظ",
              font=FONTS["subtitle"], bg=COLORS["light"]).pack(side="right")

     # مجموعة أزرار التحكم
     btn_frame = tk.Frame(toolbar, bg=COLORS["light"])
     btn_frame.pack(side="left", padx=10)

     def refresh():
          global teacher_data  # إذا كان متغيرًا عامًا
          load_data()
          teacher_data = get_teacher_data(teacher_data["username"])
          show_teacher_recitations_tab(tab, teacher_data)
          messagebox.showinfo("تم التحديث", "تم تحديث بيانات التسميعات بنجاح")

     def add_recitation():
          selected_student = student_var.get()
          if not selected_student:
               messagebox.showerror("خطأ", "يرجى اختيار طالب")
               return

          student = next((s for s in teacher_data.get("students", []) if s["name"] == selected_student), None)
          if student:
               add_recitation_dialog(student)
          else:
               messagebox.showerror("خطأ", "لم يتم العثور على الطالب")

     def add_recitation_dialog(student):
          dialog = tk.Toplevel()
          dialog.title(f"إضافة تسميع جديد للطالب {student['name']}")
          dialog.geometry("600x700")
          center_window(dialog, 600, 700)

          # إطار التمرير للنموذج
          main_frame = tk.Frame(dialog)
          main_frame.pack(fill="both", expand=True)

          canvas = tk.Canvas(main_frame)
          scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
          scrollable_frame = tk.Frame(canvas)

          scrollable_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
          canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
          canvas.configure(yscrollcommand=scrollbar.set)

          scrollbar.pack(side="left", fill="y")
          canvas.pack(side="right", fill="both", expand=True)

          tk.Label(scrollable_frame, text=f"إضافة تسميع جديد للطالب {student['name']}",
                   font=FONTS["subtitle"], fg=COLORS["primary"]).pack(pady=10)

          # الحقول المحسنة مع قيم افتراضية ذكية
          fields = [
               ("نوع التسميع", ["روتيني", "دوري", "مراجعة", "اختباري"], "روتيني"),
               ("التاريخ", get_today_date(), True),
               ("الوقت", ["صباحية", "مسائية"], "صباحية"),
               ("السورة", student.get("current_surah", ""), True),
               ("من آية", "1", True),
               ("إلى آية", "10", True),
               ("التقييم", ["ممتاز", "جيد جدًا", "جيد", "مقبول", "ضعيف"], "جيد"),
               ("عدد الأخطاء", "0", True),
               ("نوع الأخطاء", ["لا يوجد", "تكرير", "وقف", "بداية", "نهاية", "حذف", "زيادة", "قلب"], "لا يوجد"),
               ("ملاحظات المعلم", "", False),
               ("واجب منزلي", ["لا يوجد", "مراجعة", "حفظ", "تسميع"], "لا يوجد"),
               ("آيات الواجب", "", False)
          ]

          entries = []
          for label, options, default in fields:
               frame = tk.Frame(scrollable_frame)
               frame.pack(fill="x", padx=20, pady=5)

               tk.Label(frame, text=label + ":", font=FONTS["small"], width=15, anchor="e").pack(side="right")

               if isinstance(options, list):
                    var = tk.StringVar(value=default)
                    combo = ttk.Combobox(frame, textvariable=var, values=options,
                                         font=FONTS["small"], state="readonly")
                    combo.pack(fill="x")
                    entries.append(var)
               else:
                    entry = ttk.Entry(frame, font=FONTS["small"])
                    entry.insert(0, default)
                    entry.pack(fill="x")
                    entries.append(entry)

          def save_recitation():
               # التحقق من الحقول المطلوبة
               required_fields = [0, 1, 2, 3, 4, 5, 6]  # حقول مطلوبة
               for i in required_fields:
                    if not entries[i].get():
                         messagebox.showerror("خطأ", "جميع الحقول المطلوبة يجب تعبئتها")
                         return

               # إنشاء كائن التسميع الجديد
               new_recitation = {
                    "type": entries[0].get(),
                    "date": entries[1].get(),
                    "time": entries[2].get(),
                    "surah": entries[3].get(),
                    "ayahs": f"{entries[4].get()}-{entries[5].get()}",
                    "score": entries[6].get(),
                    "mistakes_count": entries[7].get(),
                    "mistakes_type": entries[8].get(),
                    "notes": entries[9].get(),
                    "homework": entries[10].get(),
                    "homework_ayahs": entries[11].get(),
                    "teacher": teacher_data["username"],
                    "added_at": f"{get_today_date()} {get_current_time()}"
               }

               # إضافة التسميع للطالب
               if "recitations" not in student:
                    student["recitations"] = []
               student["recitations"].append(new_recitation)

               # تحديث السورة الحالية إذا كانت مختلفة
               if entries[3].get() != student.get("current_surah", ""):
                    student["current_surah"] = entries[3].get()

               save_data()
               dialog.destroy()
               messagebox.showinfo("تم", "تم إضافة التسميع بنجاح")
               refresh()

          btn_frame = tk.Frame(scrollable_frame)
          btn_frame.pack(fill="x", pady=20)

          tk.Button(btn_frame, text="حفظ التسميع", font=FONTS["normal"], bg=COLORS["success"],
                    fg="white", command=save_recitation).pack(side="right", padx=10, ipadx=20)
          tk.Button(btn_frame, text="إلغاء", font=FONTS["normal"], bg=COLORS["danger"],
                    fg="white", command=dialog.destroy).pack(side="left", padx=10, ipadx=20)

     # قائمة الطلاب للاختيار مع إمكانية البحث
     students_frame = tk.Frame(btn_frame)
     students_frame.pack(side="left", padx=5)

     students = [s["name"] for s in teacher_data.get("students", [])]
     student_var = tk.StringVar()

     # إضافة حقل بحث للطلاب
     tk.Label(students_frame, text="اختر طالب:", font=FONTS["small"]).pack()
     student_combo = ttk.Combobox(students_frame, textvariable=student_var,
                                  values=students, font=FONTS["small"],
                                  state="readonly", width=20)
     student_combo.pack()

     # أزرار الشريط العلوي
     tk.Button(btn_frame, text="إضافة تسميع", font=FONTS["small"], bg=COLORS["success"],
               fg="white", command=add_recitation).pack(side="left", padx=5)
     tk.Button(btn_frame, text="تحديث البيانات", font=FONTS["small"], bg=COLORS["info"],
               fg="white", command=refresh).pack(side="left", padx=5)

     # زر تصدير البيانات
     def export_data():
          file_path = filedialog.asksaveasfilename(
               defaultextension=".xlsx",
               filetypes=[("Excel Files", "*.xlsx"), ("PDF Files", "*.pdf"), ("CSV Files", "*.csv")],
               title="حفظ تقرير التسميعات"
          )
          if file_path:
               # هنا يمكنك إضافة منطق التصدير الفعلي
               messagebox.showinfo("تم التصدير", f"تم حفظ التقرير في: {file_path}")

     tk.Button(btn_frame, text="تصدير التقرير", font=FONTS["small"], bg=COLORS["primary"],
               fg="white", command=export_data).pack(side="left", padx=5)

     # ========== إطار البحث والتصفية المتقدم ==========
     filter_frame = tk.Frame(tab, bg="white", padx=10, pady=10)
     filter_frame.pack(fill="x")

     # بحث نصي
     tk.Label(filter_frame, text="بحث:", font=FONTS["small"], bg="white").pack(side="right")
     search_var = tk.StringVar()
     search_entry = tk.Entry(filter_frame, textvariable=search_var, font=FONTS["small"], width=30)
     search_entry.pack(side="right", padx=5)

     # تصفية حسب التاريخ
     tk.Label(filter_frame, text="الفترة الزمنية:", font=FONTS["small"], bg="white").pack(side="right", padx=10)
     date_range_var = tk.StringVar(value="الكل")
     date_range_combo = ttk.Combobox(filter_frame, textvariable=date_range_var,
                                     values=["الكل", "اليوم", "أمس", "هذا الأسبوع", "هذا الشهر", "اختيار تاريخ"],
                                     font=FONTS["small"], state="readonly", width=15)
     date_range_combo.pack(side="right")

     # تصفية حسب التقييم
     tk.Label(filter_frame, text="التقييم:", font=FONTS["small"], bg="white").pack(side="right", padx=10)
     score_var = tk.StringVar(value="الكل")
     score_combo = ttk.Combobox(filter_frame, textvariable=score_var,
                                values=["الكل", "ممتاز", "جيد جدًا", "جيد", "مقبول", "ضعيف"],
                                font=FONTS["small"], state="readonly", width=15)
     score_combo.pack(side="right")

     # تصفية حسب نوع التسميع
     tk.Label(filter_frame, text="نوع التسميع:", font=FONTS["small"], bg="white").pack(side="right", padx=10)
     type_var = tk.StringVar(value="الكل")
     type_combo = ttk.Combobox(filter_frame, textvariable=type_var,
                               values=["الكل", "روتيني", "دوري", "مراجعة", "اختباري"],
                               font=FONTS["small"], state="readonly", width=15)
     type_combo.pack(side="right")

     # ========== جدول التسميعات مع تحسينات ==========
     tree_container = tk.Frame(tab)
     tree_container.pack(fill="both", expand=True, padx=10, pady=(0, 10))

     # أعمدة الجدول المحسنة
     cols = ("الطالب", "التاريخ", "الوقت", "النوع", "السورة", "الآيات", "التقييم", "الأخطاء", "الواجب", "ملاحظات")
     tree = ttk.Treeview(tree_container, columns=cols, show="headings", style="Custom.Treeview", height=15)

     # تحديد عرض الأعمدة
     col_widths = {
          "الطالب": 120,
          "التاريخ": 100,
          "الوقت": 80,
          "النوع": 80,
          "السورة": 120,
          "الآيات": 80,
          "التقييم": 80,
          "الأخطاء": 100,
          "الواجب": 100,
          "ملاحظات": 150
     }

     for col in cols:
          tree.heading(col, text=col)
          tree.column(col, width=col_widths.get(col, 100), anchor="center")

     # شريط التمرير
     scroll_y = ttk.Scrollbar(tree_container, orient="vertical", command=tree.yview)
     scroll_x = ttk.Scrollbar(tree_container, orient="horizontal", command=tree.xview)
     tree.configure(yscrollcommand=scroll_y.set, xscrollcommand=scroll_x.set)

     # تخطيط العناصر
     tree.grid(row=0, column=0, sticky="nsew")
     scroll_y.grid(row=0, column=1, sticky="ns")
     scroll_x.grid(row=1, column=0, sticky="ew")

     tree_container.grid_rowconfigure(0, weight=1)
     tree_container.grid_columnconfigure(0, weight=1)

     # تعبئة الجدول بالبيانات
     def load_recitations_data():
          for item in tree.get_children():
               tree.delete(item)

          students = teacher_data.get("students", [])
          for student in students:
               for rec in student.get("recitations", []):
                    tree.insert("", "end", values=(
                         student["name"],
                         rec.get("date", ""),
                         rec.get("time", ""),
                         rec.get("type", ""),
                         rec.get("surah", ""),
                         rec.get("ayahs", ""),
                         rec.get("score", ""),
                         f"{rec.get('mistakes_count', '')} ({rec.get('mistakes_type', '')})",
                         rec.get("homework", ""),
                         rec.get("notes", "")
                    ), tags=(rec.get("score", ""),))

          # تلوين الصفوف حسب التقييم
          tree.tag_configure("ممتاز", background="#e6f7e6")
          tree.tag_configure("جيد جدًا", background="#f0f7e6")
          tree.tag_configure("جيد", background="#f7f7e6")
          tree.tag_configure("مقبول", background="#f7f0e6")
          tree.tag_configure("ضعيف", background="#f7e6e6")

     load_recitations_data()

     # وظيفة البحث والتصفية المتقدمة
     def filter_recitations(*args):
          search_text = search_var.get().lower()
          date_range = date_range_var.get()
          score_filter = score_var.get()
          type_filter = type_var.get()
          for item in tree.get_children():
               tree.delete(item)

          students = teacher_data.get("students", [])
          for student in students:
               for rec in student.get("recitations", []):
                    # تطبيق الفلاتر
                    date_ok = True
                    if date_range != "الكل":
                         rec_date = datetime.datetime.strptime(rec.get("date", "2000-01-01"), "%Y-%m-%d")
                         today = datetime.datetime.now()

                         if date_range == "اليوم" and rec_date.date() != today.date():
                              date_ok = False
                         elif date_range == "أمس" and rec_date.date() != (today - datetime.timedelta(days=1)).date():
                              date_ok = False
                         elif date_range == "هذا الأسبوع" and rec_date < (today - datetime.timedelta(days=7)):
                              date_ok = False
                         elif date_range == "هذا الشهر" and rec_date < (today - datetime.timedelta(days=30)):
                              date_ok = False
                         elif date_range == "اختيار تاريخ":
                              # يمكن إضافة نافذة اختيار تاريخ هنا
                              pass

                    score_ok = (score_filter == "الكل" or rec.get("score", "") == score_filter)
                    type_ok = (type_filter == "الكل" or rec.get("type", "") == type_filter)
                    search_ok = (search_text in student["name"].lower() or
                                 search_text in rec.get("surah", "").lower() or
                                 search_text in rec.get("notes", "").lower() or
                                 search_text in rec.get("type", "").lower())

                    if date_ok and score_ok and type_ok and search_ok:
                         tree.insert("", "end", values=(
                              student["name"],
                              rec.get("date", ""),
                              rec.get("time", ""),
                              rec.get("type", ""),
                              rec.get("surah", ""),
                              rec.get("ayahs", ""),
                              rec.get("score", ""),
                              f"{rec.get('mistakes_count', '')} ({rec.get('mistakes_type', '')})",
                              rec.get("homework", ""),
                              rec.get("notes", "")
                         ), tags=(rec.get("score", ""),))

     # ربط أحداث التغيير
     search_var.trace("w", filter_recitations)
     date_range_var.trace("w", filter_recitations)
     score_var.trace("w", filter_recitations)
     type_var.trace("w", filter_recitations)

     # ========== أزرار التحكم السفلية ==========
     control_frame = tk.Frame(tab)
     control_frame.pack(fill="x", pady=10)

     def edit_selected():
          selected = tree.selection()
          if not selected:
               messagebox.showerror("خطأ", "يرجى اختيار تسميع لتعديله")
               return

          student_name = tree.item(selected[0])["values"][0]
          rec_date = tree.item(selected[0])["values"][1]
          rec_time = tree.item(selected[0])["values"][2]

          student = next((s for s in teacher_data.get("students", []) if s["name"] == student_name), None)
          if not student:
               messagebox.showerror("خطأ", "لم يتم العثور على الطالب")
               return

          recitation = next((r for r in student.get("recitations", [])
                             if r.get("date") == rec_date and r.get("time") == rec_time), None)
          if not recitation:
               messagebox.showerror("خطأ", "لم يتم العثور على التسميع")
               return

          dialog = tk.Toplevel()
          dialog.title(f"تعديل تسميع الطالب {student_name}")
          dialog.geometry("600x700")
          center_window(dialog, 600, 700)

          # إطار التمرير للنموذج
          main_frame = tk.Frame(dialog)
          main_frame.pack(fill="both", expand=True)

          canvas = tk.Canvas(main_frame)
          scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
          scrollable_frame = tk.Frame(canvas)

          scrollable_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
          canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
          canvas.configure(yscrollcommand=scrollbar.set)

          scrollbar.pack(side="left", fill="y")
          canvas.pack(side="right", fill="both", expand=True)

          tk.Label(scrollable_frame, text=f"تعديل تسميع الطالب {student_name}",
                   font=FONTS["subtitle"], fg=COLORS["primary"]).pack(pady=10)

          # الحقول المحسنة
          fields = [
               ("نوع التسميع", ["روتيني", "دوري", "مراجعة", "اختباري"], recitation.get("type", ""), True),
               ("التاريخ", recitation.get("date", ""), True),
               ("الوقت", ["صباحية", "مسائية"], recitation.get("time", "صباحية"), True),
               ("السورة", recitation.get("surah", ""), True),
               ("من آية", recitation.get("ayahs", "").split("-")[0] if "-" in recitation.get("ayahs", "") else "", True),
               ("إلى آية", recitation.get("ayahs", "").split("-")[1] if "-" in recitation.get("ayahs", "") else "", True),
               ("التقييم", ["ممتاز", "جيد جدًا", "جيد", "مقبول", "ضعيف"], recitation.get("score", ""), True),
               ("عدد الأخطاء", recitation.get("mistakes_count", "0"), True),
               ("نوع الأخطاء", ["لا يوجد", "تكرير", "وقف", "بداية", "نهاية", "حذف", "زيادة", "قلب"],
                recitation.get("mistakes_type", "لا يوجد"), True),
               ("ملاحظات المعلم", recitation.get("notes", ""), False),
               ("واجب منزلي", ["لا يوجد", "مراجعة", "حفظ", "تسميع"], recitation.get("homework", "لا يوجد"), True),
               ("آيات الواجب", recitation.get("homework_ayahs", ""), False)
          ]

          entries = []
          for label, options, default, required in fields:
               frame = tk.Frame(scrollable_frame)
               frame.pack(fill="x", padx=20, pady=5)

               tk.Label(frame, text=label + ":", font=FONTS["small"], width=15, anchor="e").pack(side="right")

               if isinstance(options, list):
                    var = tk.StringVar(value=default)
                    combo = ttk.Combobox(frame, textvariable=var, values=options,
                                         font=FONTS["small"], state="readonly")
                    combo.pack(fill="x")
                    entries.append(var)
               else:
                    entry = ttk.Entry(frame, font=FONTS["small"])
                    entry.insert(0, default)
                    entry.pack(fill="x")
                    entries.append(entry)

          def update_recitation():
               # التحقق من الحقول المطلوبة
               required_fields = [0, 1, 2, 3, 4, 5, 6]  # حقول مطلوبة
               for i in required_fields:
                    if not entries[i].get():
                         messagebox.showerror("خطأ", "جميع الحقول المطلوبة يجب تعبئتها")
                         return

               # تحديث بيانات التسميع
               recitation.update({
                    "type": entries[0].get(),
                    "date": entries[1].get(),
                    "time": entries[2].get(),
                    "surah": entries[3].get(),
                    "ayahs": f"{entries[4].get()}-{entries[5].get()}",
                    "score": entries[6].get(),
                    "mistakes_count": entries[7].get(),
                    "mistakes_type": entries[8].get(),
                    "notes": entries[9].get(),
                    "homework": entries[10].get(),
                    "homework_ayahs": entries[11].get(),
                    "updated_at": f"{get_today_date()} {get_current_time()}"
               })

               save_data()
               dialog.destroy()
               messagebox.showinfo("تم", "تم تحديث التسميع بنجاح")
               refresh()

          btn_frame = tk.Frame(scrollable_frame)
          btn_frame.pack(fill="x", pady=20)

          tk.Button(btn_frame, text="حفظ التعديلات", font=FONTS["normal"], bg=COLORS["success"],
                    fg="white", command=update_recitation).pack(side="right", padx=10, ipadx=20)
          tk.Button(btn_frame, text="إلغاء", font=FONTS["normal"], bg=COLORS["danger"],
                    fg="white", command=dialog.destroy).pack(side="left", padx=10, ipadx=20)

     def delete_selected():
          selected = tree.selection()
          if not selected:
               messagebox.showerror("خطأ", "يرجى اختيار تسميع لحذفه")
               return

          student_name = tree.item(selected[0])["values"][0]
          rec_date = tree.item(selected[0])["values"][1]
          rec_time = tree.item(selected[0])["values"][2]

          if messagebox.askyesno("تأكيد", f"هل أنت متأكد من حذف تسميع الطالب {student_name} بتاريخ {rec_date}؟"):
               student = next((s for s in teacher_data.get("students", []) if s["name"] == student_name), None)
               if student:
                    student["recitations"] = [r for r in student.get("recitations", [])
                                              if not (r.get("date") == rec_date and r.get("time") == rec_time)]
                    save_data()
                    messagebox.showinfo("تم", "تم حذف التسميع بنجاح")
                    refresh()
               else:
                    messagebox.showerror("خطأ", "لم يتم العثور على الطالب")

     def print_report():
          selected = tree.selection()
          if not selected:
               messagebox.showerror("خطأ", "يرجى اختيار تسميع لطباعة تقريره")
               return

          student_name = tree.item(selected[0])["values"][0]
          rec_date = tree.item(selected[0])["values"][1]
          rec_time = tree.item(selected[0])["values"][2]

          student = next((s for s in teacher_data.get("students", []) if s["name"] == student_name), None)
          if not student:
               messagebox.showerror("خطأ", "لم يتم العثور على الطالب")
               return

          recitation = next((r for r in student.get("recitations", [])
                             if r.get("date") == rec_date and r.get("time") == rec_time), None)
          if not recitation:
               messagebox.showerror("خطأ", "لم يتم العثور على التسميع")
               return

          # إنشاء نافذة التقرير
          report_window = tk.Toplevel()
          report_window.title(f"تقرير تسميع الطالب {student_name}")
          report_window.geometry("600x800")
          center_window(report_window, 600, 800)

          # محتوى التقرير
          report_frame = tk.Frame(report_window)
          report_frame.pack(fill="both", expand=True, padx=20, pady=20)

          # عنوان التقرير
          tk.Label(report_frame, text=f"تقرير تسميع الطالب {student_name}",
                   font=FONTS["title"], fg=COLORS["primary"]).pack(pady=10)

          # معلومات الطالب
          student_frame = tk.Frame(report_frame, bd=1, relief="solid", padx=10, pady=10)
          student_frame.pack(fill="x", pady=5)

          tk.Label(student_frame, text="معلومات الطالب:", font=FONTS["subtitle"]).pack(anchor="w")
          tk.Label(student_frame,
                   text=f"الاسم: {student['name']}\nالمستوى: {student.get('level', '')}\nالسورة الحالية: {student.get('current_surah', '')}",
                   font=FONTS["normal"], justify="right").pack(anchor="e")

          # معلومات التسميع
          recitation_frame = tk.Frame(report_frame, bd=1, relief="solid", padx=10, pady=10)
          recitation_frame.pack(fill="x", pady=5)

          tk.Label(recitation_frame, text="معلومات التسميع:", font=FONTS["subtitle"]).pack(anchor="w")
          tk.Label(recitation_frame,
                   text=f"التاريخ: {recitation.get('date', '')}\nالنوع: {recitation.get('type', '')}\nالسورة: {recitation.get('surah', '')}\nالآيات: {recitation.get('ayahs', '')}",
                   font=FONTS["normal"], justify="right").pack(anchor="e")

          # التقييم والأخطاء
          evaluation_frame = tk.Frame(report_frame, bd=1, relief="solid", padx=10, pady=10)
          evaluation_frame.pack(fill="x", pady=5)

          tk.Label(evaluation_frame, text="التقييم والأخطاء:", font=FONTS["subtitle"]).pack(anchor="w")
          tk.Label(evaluation_frame,
                   text=f"التقييم: {recitation.get('score', '')}\nعدد الأخطاء: {recitation.get('mistakes_count', '')}\nنوع الأخطاء: {recitation.get('mistakes_type', '')}",
                   font=FONTS["normal"], justify="right").pack(anchor="e")

          # الملاحظات والواجب
          notes_frame = tk.Frame(report_frame, bd=1, relief="solid", padx=10, pady=10)
          notes_frame.pack(fill="x", pady=5)

          tk.Label(notes_frame, text="ملاحظات المعلم:", font=FONTS["subtitle"]).pack(anchor="w")
          tk.Label(notes_frame, text=recitation.get("notes", "لا توجد ملاحظات"),
                   font=FONTS["normal"], wraplength=550, justify="right").pack(anchor="e")

          homework_frame = tk.Frame(report_frame, bd=1, relief="solid", padx=10, pady=10)
          homework_frame.pack(fill="x", pady=5)

          tk.Label(homework_frame, text="الواجب المنزلي:", font=FONTS["subtitle"]).pack(anchor="w")
          tk.Label(homework_frame,
                   text=f"{recitation.get('homework', 'لا يوجد')}\n{recitation.get('homework_ayahs', '')}",
                   font=FONTS["normal"], wraplength=550, justify="right").pack(anchor="e")

          # توقيع المعلم
          signature_frame = tk.Frame(report_frame)
          signature_frame.pack(fill="x", pady=20)

          tk.Label(signature_frame, text=f"توقيع المعلم: {teacher_data.get('full_name', '')}",
                   font=FONTS["normal"]).pack(side="right", padx=50)

          # أزرار التحكم في التقرير
          btn_frame = tk.Frame(report_window)
          btn_frame.pack(fill="x", pady=10)

          def print_document():
               messagebox.showinfo("طباعة", "سيتم إرسال التقرير إلى الطابعة")

          def save_pdf():
               file_path = filedialog.asksaveasfilename(
                    defaultextension=".pdf",
                    filetypes=[("PDF Files", "*.pdf")],
                    title="حفظ التقرير كملف PDF"
               )
               if file_path:
                    messagebox.showinfo("تم الحفظ", f"تم حفظ التقرير في: {file_path}")

          tk.Button(btn_frame, text="طباعة التقرير", font=FONTS["small"], bg=COLORS["primary"],
                    fg="white", command=print_document).pack(side="right", padx=10)
          tk.Button(btn_frame, text="حفظ كـ PDF", font=FONTS["small"], bg=COLORS["secondary"],
                    fg="white", command=save_pdf).pack(side="right", padx=10)

     # أزرار التحكم
     tk.Button(control_frame, text="تعديل المحدد", font=FONTS["small"], bg=COLORS["warning"],
               fg="white", command=edit_selected).pack(side="right", padx=10)
     tk.Button(control_frame, text="حذف المحدد", font=FONTS["small"], bg=COLORS["danger"],
               fg="white", command=delete_selected).pack(side="right", padx=10)
     tk.Button(control_frame, text="طباعة التقرير", font=FONTS["small"], bg=COLORS["primary"],
               fg="white", command=print_report).pack(side="right", padx=10)
     tk.Button(control_frame, text="إحصائيات التسميع", font=FONTS["small"], bg=COLORS["info"],
               fg="white").pack(side="right", padx=10)

     # ========== إحصائيات سريعة ==========
     stats_frame = tk.Frame(tab, bg=COLORS["light"], padx=10, pady=10)
     stats_frame.pack(fill="x")

     # حساب الإحصائيات
     total_recitations = sum(len(s.get("recitations", [])) for s in teacher_data.get("students", []))
     today_recitations = sum(1 for s in teacher_data.get("students", [])
                             for r in s.get("recitations", [])
                             if r.get("date") == get_today_date())
     excellent_count = sum(1 for s in teacher_data.get("students", [])
                           for r in s.get("recitations", [])
                           if r.get("score") == "ممتاز")
     mistakes_count = sum(int(r.get("mistakes_count", 0)) for s in teacher_data.get("students", [])
                          for r in s.get("recitations", []))

     stats = [
          ("إجمالي التسميعات", total_recitations, COLORS["primary"]),
          ("تسميعات اليوم", today_recitations, COLORS["secondary"]),
          ("تسميعات ممتاز", excellent_count, COLORS["success"]),
          ("إجمالي الأخطاء", mistakes_count, COLORS["danger"])
     ]

     for i, (label, value, color) in enumerate(stats):
          stat_frame = tk.Frame(stats_frame, bg=color, bd=1, relief="ridge", width=180, height=60)
          stat_frame.grid(row=0, column=i, padx=5, pady=5, sticky="nsew")
          stat_frame.grid_propagate(False)

          tk.Label(stat_frame, text=label, font=FONTS["small"], bg=color, fg="white").pack(pady=(5, 0))
          tk.Label(stat_frame, text=str(value), font=("Cairo", 14, "bold"), bg=color, fg="white").pack()

     # جعل الأعمدة متساوية في الحجم
     for i in range(len(stats)):
          stats_frame.grid_columnconfigure(i, weight=1)

def show_teacher_attendance_tab(tab, teacher_data):
     tab.pack_propagate(False)

     # تنظيف المحتوى السابق إذا وجد
     for widget in tab.winfo_children():
          widget.destroy()

     # ========== شريط الأدوات مع تحسينات ==========
     toolbar = tk.Frame(tab, bg=COLORS["light"], pady=5)
     toolbar.pack(fill="x")

     # العنوان مع أيقونة
     title_frame = tk.Frame(toolbar, bg=COLORS["light"])
     title_frame.pack(side="right", padx=10)
     tk.Label(title_frame, text="✅ سجل الحضور",
              font=FONTS["subtitle"], bg=COLORS["light"]).pack(side="right")

     # مجموعة أزرار التحكم
     btn_frame = tk.Frame(toolbar, bg=COLORS["light"])
     btn_frame.pack(side="left", padx=10)

     def refresh():
          global teacher_data
          load_data()
          teacher_data = get_teacher_data(teacher_data["username"])
          show_teacher_attendance_tab(tab, teacher_data)

     def mark_attendance():
          dialog = tk.Toplevel()
          dialog.title("تسجيل الحضور")
          dialog.geometry("500x600")
          center_window(dialog, 500, 600)

          tk.Label(dialog, text="تسجيل الحضور اليومي",
                   font=FONTS["subtitle"], fg=COLORS["primary"]).pack(pady=10)

          # تاريخ اليوم
          today = get_today_date()
          tk.Label(dialog, text=f"تاريخ اليوم: {today}",
                   font=FONTS["normal"]).pack(pady=5)

          # إطار التمرير للطلاب
          main_frame = tk.Frame(dialog)
          main_frame.pack(fill="both", expand=True)

          canvas = tk.Canvas(main_frame)
          scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
          scrollable_frame = tk.Frame(canvas)

          scrollable_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
          canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
          canvas.configure(yscrollcommand=scrollbar.set)

          scrollbar.pack(side="left", fill="y")
          canvas.pack(side="right", fill="both", expand=True)

          # قائمة الطلاب مع خيارات الحضور
          attendance_vars = {}
          students = teacher_data.get("students", [])

          for student in students:
               frame = tk.Frame(scrollable_frame)
               frame.pack(fill="x", padx=20, pady=5)

               # التحقق إذا كان الطالب قد سجل حضور اليوم
               today_attendance = next((a for a in student.get("attendance", [])
                                        if a.get("date") == today), None)

               var = tk.StringVar(value="حاضر" if not today_attendance else today_attendance.get("status", "حاضر"))
               attendance_vars[student["name"]] = var

               tk.Label(frame, text=student["name"],
                        font=FONTS["small"], width=25, anchor="e").pack(side="right")

               combo = ttk.Combobox(frame, textvariable=var,
                                    values=["حاضر", "غائب", "إجازة", "متأخر"],
                                    font=FONTS["small"], state="readonly", width=10)
               combo.pack(side="right")

          def save_attendance():
               for student in teacher_data.get("students", []):
                    student_name = student["name"]
                    status = attendance_vars[student_name].get()

                    # البحث عن سجل الحضور لهذا اليوم
                    today_attendance = next((a for a in student.get("attendance", [])
                                             if a.get("date") == today), None)

                    if today_attendance:
                         # تحديث حالة الحضور إذا كانت موجودة
                         today_attendance["status"] = status
                    else:
                         # إضافة سجل حضور جديد
                         if "attendance" not in student:
                              student["attendance"] = []
                         student["attendance"].append({
                              "date": today,
                              "status": status,
                              "recorded_by": teacher_data["username"]
                         })

               save_data()
               dialog.destroy()
               messagebox.showinfo("تم", "تم تسجيل الحضور بنجاح")
               refresh()

          btn_frame = tk.Frame(dialog)
          btn_frame.pack(fill="x", pady=20)

          tk.Button(btn_frame, text="حفظ", font=FONTS["normal"], bg=COLORS["success"],
                    fg="white", command=save_attendance).pack(side="right", padx=10, ipadx=20)
          tk.Button(btn_frame, text="إلغاء", font=FONTS["normal"], bg=COLORS["danger"],
                    fg="white", command=dialog.destroy).pack(side="left", padx=10, ipadx=20)

     # أزرار الشريط العلوي
     tk.Button(btn_frame, text="تسجيل الحضور", font=FONTS["small"], bg=COLORS["success"],
               fg="white", command=mark_attendance).pack(side="left", padx=5)
     tk.Button(btn_frame, text="تحديث", font=FONTS["small"], bg=COLORS["info"],
               fg="white", command=refresh).pack(side="left", padx=5)

     # ========== إطار البحث والتصفية ==========
     filter_frame = tk.Frame(tab, bg="white", padx=10, pady=10)
     filter_frame.pack(fill="x")

     tk.Label(filter_frame, text="بحث:", font=FONTS["small"], bg="white").pack(side="right")
     search_var = tk.StringVar()
     search_entry = tk.Entry(filter_frame, textvariable=search_var, font=FONTS["small"], width=30)
     search_entry.pack(side="right", padx=5)

     tk.Label(filter_frame, text="تصفية حسب التاريخ:", font=FONTS["small"], bg="white").pack(side="right", padx=10)
     date_var = tk.StringVar(value=get_today_date())
     date_combo = ttk.Combobox(filter_frame, textvariable=date_var,
                               values=[get_today_date(), "هذا الأسبوع", "هذا الشهر", "الكل"],
                               font=FONTS["small"], state="readonly", width=15)
     date_combo.pack(side="right")

     # ========== جدول الحضور مع تحسينات ==========
     tree_container = tk.Frame(tab)
     tree_container.pack(fill="both", expand=True, padx=10, pady=(0, 10))

     cols = ("الطالب", "التاريخ", "الحالة", "مسجل بواسطة")
     tree = ttk.Treeview(tree_container, columns=cols, show="headings", style="Custom.Treeview", height=15)

     # تحديد عرض الأعمدة
     col_widths = {
          "الطالب": 150,
          "التاريخ": 120,
          "الحالة": 100,
          "مسجل بواسطة": 120
     }

     for col in cols:
          tree.heading(col, text=col)
          tree.column(col, width=col_widths.get(col, 120), anchor="center")

     scroll = ttk.Scrollbar(tree_container, orient="vertical", command=tree.yview)
     tree.configure(yscrollcommand=scroll.set)
     scroll.pack(side="left", fill="y")
     tree.pack(side="right", fill="both", expand=True)

     # تعبئة الجدول بالبيانات
     def load_attendance_data():
          for item in tree.get_children():
               tree.delete(item)

          students = teacher_data.get("students", [])
          for student in students:
               for attendance in student.get("attendance", []):
                    tree.insert("", "end", values=(
                         student["name"],
                         attendance.get("date", ""),
                         attendance.get("status", ""),
                         attendance.get("recorded_by", "")
                    ), tags=(attendance.get("status", ""),))

          # تلوين الصفوف حسب حالة الحضور
          tree.tag_configure("حاضر", background="#e6f7e6")
          tree.tag_configure("غائب", background="#ffdddd")
          tree.tag_configure("إجازة", background="#e6f0f7")
          tree.tag_configure("متأخر", background="#fff7e6")

     load_attendance_data()

     # وظيفة البحث والتصفية
     def filter_attendance(*args):
          search_text = search_var.get().lower()
          date_filter = date_var.get()

          for item in tree.get_children():
               tree.delete(item)

          students = teacher_data.get("students", [])
          for student in students:
               for attendance in student.get("attendance", []):
                    # تطبيق الفلاتر
                    date_ok = (date_filter == "الكل" or
                               (date_filter == "هذا الأسبوع" and datetime.datetime.strptime(
                                    attendance.get("date", "2000-01-01"),
                                    "%Y-%m-%d") >= datetime.datetime.now() - datetime.timedelta(days=7)) or
                               (date_filter == "هذا الشهر" and datetime.datetime.strptime(
                                    attendance.get("date", "2000-01-01"),
                                    "%Y-%m-%d") >= datetime.datetime.now() - datetime.timedelta(days=30)) or
                               attendance.get("date", "") == date_filter)

                    search_ok = (search_text in student["name"].lower() or
                                 search_text in attendance.get("status", "").lower())

                    if date_ok and search_ok:
                         tree.insert("", "end", values=(
                              student["name"],
                              attendance.get("date", ""),
                              attendance.get("status", ""),
                              attendance.get("recorded_by", "")
                         ), tags=(attendance.get("status", ""),))

     search_var.trace("w", filter_attendance)
     date_var.trace("w", filter_attendance)

     # ========== أزرار التحكم السفلية ==========
     control_frame = tk.Frame(tab)
     control_frame.pack(fill="x", pady=10)

     def edit_selected():
          selected = tree.selection()
          if not selected:
               messagebox.showerror("خطأ", "يرجى اختيار سجل حضور لتعديله")
               return

          student_name = tree.item(selected[0])["values"][0]
          att_date = tree.item(selected[0])["values"][1]

          student = next((s for s in teacher_data.get("students", []) if s["name"] == student_name), None)
          if not student:
               messagebox.showerror("خطأ", "لم يتم العثور على الطالب")
               return

          attendance = next((a for a in student.get("attendance", []) if a.get("date") == att_date), None)
          if not attendance:
               messagebox.showerror("خطأ", "لم يتم العثور على سجل الحضور")
               return

          dialog = tk.Toplevel()
          dialog.title(f"تعديل حضور الطالب {student_name}")
          dialog.geometry("400x300")
          center_window(dialog, 400, 300)

          tk.Label(dialog, text=f"تعديل حضور الطالب {student_name}",
                   font=FONTS["subtitle"], fg=COLORS["primary"]).pack(pady=10)

          tk.Label(dialog, text=f"التاريخ: {att_date}",
                   font=FONTS["normal"]).pack(pady=5)

          frame = tk.Frame(dialog)
          frame.pack(pady=10)

          tk.Label(frame, text="الحالة:", font=FONTS["small"], width=15, anchor="e").pack(side="right")
          status_var = tk.StringVar(value=attendance.get("status", "حاضر"))
          status_combo = ttk.Combobox(frame, textvariable=status_var,
                                      values=["حاضر", "غائب", "إجازة", "متأخر"],
                                      font=FONTS["small"], state="readonly", width=10)
          status_combo.pack(side="right")

          def update_attendance():
               attendance["status"] = status_var.get()
               save_data()
               dialog.destroy()
               messagebox.showinfo("تم", "تم تعديل سجل الحضور بنجاح")
               refresh()

          btn_frame = tk.Frame(dialog)
          btn_frame.pack(fill="x", pady=20)

          tk.Button(btn_frame, text="حفظ التعديلات", font=FONTS["normal"], bg=COLORS["success"],
                    fg="white", command=update_attendance).pack(side="right", padx=10, ipadx=20)
          tk.Button(btn_frame, text="إلغاء", font=FONTS["normal"], bg=COLORS["danger"],
                    fg="white", command=dialog.destroy).pack(side="left", padx=10, ipadx=20)

     def delete_selected():
          selected = tree.selection()
          if not selected:
               messagebox.showerror("خطأ", "يرجى اختيار سجل حضور لحذفه")
               return

          student_name = tree.item(selected[0])["values"][0]
          att_date = tree.item(selected[0])["values"][1]

          if messagebox.askyesno("تأكيد", f"هل أنت متأكد من حذف سجل حضور الطالب {student_name} بتاريخ {att_date}؟"):
               student = next((s for s in teacher_data.get("students", []) if s["name"] == student_name), None)
               if student:
                    student["attendance"] = [a for a in student.get("attendance", []) if a.get("date") != att_date]
                    save_data()
                    messagebox.showinfo("تم", "تم حذف سجل الحضور بنجاح")
                    refresh()
               else:
                    messagebox.showerror("خطأ", "لم يتم العثور على الطالب")

     # أزرار التحكم
     tk.Button(control_frame, text="تعديل المحدد", font=FONTS["small"], bg=COLORS["warning"],
               fg="white", command=edit_selected).pack(side="right", padx=10)
     tk.Button(control_frame, text="حذف المحدد", font=FONTS["small"], bg=COLORS["danger"],
               fg="white", command=delete_selected).pack(side="right", padx=10)
     tk.Button(control_frame, text="طباعة التقرير", font=FONTS["small"], bg=COLORS["primary"],
               fg="white").pack(side="right", padx=10)

def show_teacher_reports_tab(tab, teacher_data):
     tab.pack_propagate(False)

     # تنظيف المحتوى السابق إذا وجد
     for widget in tab.winfo_children():
          widget.destroy()

     # ========== شريط الأدوات مع تحسينات ==========
     toolbar = tk.Frame(tab, bg=COLORS["light"], pady=5)
     toolbar.pack(fill="x")

     # العنوان مع أيقونة
     title_frame = tk.Frame(toolbar, bg=COLORS["light"])
     title_frame.pack(side="right", padx=10)
     tk.Label(title_frame, text="📊 التقارير والإحصائيات",
              font=FONTS["subtitle"], bg=COLORS["light"]).pack(side="right")

     # مجموعة أزرار التحكم
     btn_frame = tk.Frame(toolbar, bg=COLORS["light"])
     btn_frame.pack(side="left", padx=10)

     def refresh():
          global teacher_data
          load_data()
          teacher_data = get_teacher_data(teacher_data["username"])
          show_teacher_reports_tab(tab, teacher_data)

     # أزرار الشريط العلوي
     tk.Button(btn_frame, text="تحديث", font=FONTS["small"], bg=COLORS["info"],
               fg="white", command=refresh).pack(side="left", padx=5)

     # ========== تبويبات التقارير مع تحسينات ==========
     report_nb = ttk.Notebook(tab)
     report_nb.pack(fill="both", expand=True)

     # تبويب تقارير الطلاب
     students_frame = tk.Frame(report_nb)
     report_nb.add(students_frame, text="🧑‍🎓 تقارير الطلاب")

     # إحصائيات الطلاب
     students_stats_frame = tk.Frame(students_frame, bg="white", bd=1, relief="solid")
     students_stats_frame.pack(fill="x", padx=20, pady=20)

     tk.Label(students_stats_frame, text="إحصائيات الطلاب",
              font=FONTS["subtitle"], bg="white").pack(pady=10)

     # رسم بياني مبسط للإحصائيات
     canvas = tk.Canvas(students_stats_frame, width=800, height=200, bg="white")
     canvas.pack(pady=10)

     students = teacher_data.get("students", [])
     stats = {
          "ممتاز": sum(1 for s in students if s.get("performance") == "ممتاز"),
          "جيد جدًا": sum(1 for s in students if s.get("performance") == "جيد جدًا"),
          "جيد": sum(1 for s in students if s.get("performance") == "جيد"),
          "مقبول": sum(1 for s in students if s.get("performance") == "مقبول")
     }

     # رسم أعمدة بيانية مبسطة
     max_value = max(stats.values()) or 1
     bar_height = lambda val: (val / max_value) * 180

     colors = [COLORS["success"], COLORS["info"], COLORS["warning"], COLORS["danger"]]
     x_pos = 100

     for (label, value), color in zip(stats.items(), colors):
          canvas.create_rectangle(x_pos, 200 - bar_height(value), x_pos + 80, 200,
                                  fill=color, outline="")
          canvas.create_text(x_pos + 40, 180 - bar_height(value),
                             text=str(value), font=FONTS["small"])
          canvas.create_text(x_pos + 40, 220, text=label, font=FONTS["small"])
          x_pos += 150

     # تبويب تقارير التسميع
     recitations_frame = tk.Frame(report_nb)
     report_nb.add(recitations_frame, text="📖 تقارير التسميع")

     # إحصائيات التسميع
     rec_stats_frame = tk.Frame(recitations_frame, bg="white", bd=1, relief="solid")
     rec_stats_frame.pack(fill="x", padx=20, pady=20)

     tk.Label(rec_stats_frame, text="إحصائيات التسميع",
              font=FONTS["subtitle"], bg="white").pack(pady=10)

     # حساب عدد التسميعات لكل شهر
     monthly_rec = {}
     for student in students:
          for rec in student.get("recitations", []):
               month = rec.get("date", "")[:7]  # YYYY-MM
               if month not in monthly_rec:
                    monthly_rec[month] = 0
               monthly_rec[month] += 1

     # تحويل إلى قائمة مرتبة
     months = sorted(monthly_rec.keys())
     values = [monthly_rec[m] for m in months]

     if months:
          # رسم بياني خطي مبسط
          canvas = tk.Canvas(rec_stats_frame, width=800, height=200, bg="white")
          canvas.pack(pady=10)

          max_val = max(values) or 1
          scale = 180 / max_val
          x_scale = 700 / len(months)

          prev_x, prev_y = 50, 200 - values[0] * scale
          for i, (month, val) in enumerate(zip(months, values)):
               x = 50 + i * x_scale
               y = 200 - val * scale
               canvas.create_line(prev_x, prev_y, x, y, fill=COLORS["primary"], width=2)
               canvas.create_oval(x - 3, y - 3, x + 3, y + 3, fill=COLORS["secondary"])
               canvas.create_text(x, 210, text=month[5:], font=FONTS["small"])  # عرض الشهر فقط (MM)
               prev_x, prev_y = x, y

     # ========== أزرار التحكم مع تحسينات ==========
     control_frame = tk.Frame(tab)
     control_frame.pack(fill="x", pady=10)

     def print_report():
          messagebox.showinfo("طباعة", "سيتم طباعة التقرير الحالي")

     def export_report():
          file_path = filedialog.asksaveasfilename(
               defaultextension=".pdf",
               filetypes=[("PDF Files", "*.pdf"), ("Excel Files", "*.xlsx")],
               title="حفظ التقرير"
          )
          if file_path:
               messagebox.showinfo("تصدير", f"سيتم تصدير التقرير إلى {file_path}")

     def generate_detailed_report():
          dialog = tk.Toplevel()
          dialog.title("تقرير مفصل")
          dialog.geometry("800x600")
          center_window(dialog, 800, 600)

          # إطار التمرير
          main_frame = tk.Frame(dialog)
          main_frame.pack(fill="both", expand=True)

          canvas = tk.Canvas(main_frame)
          scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
          scrollable_frame = tk.Frame(canvas)

          scrollable_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
          canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
          canvas.configure(yscrollcommand=scrollbar.set)

          scrollbar.pack(side="left", fill="y")
          canvas.pack(side="right", fill="both", expand=True)

          # محتوى التقرير المفصل
          tk.Label(scrollable_frame, text=f"تقرير أداء الطلاب - المعلم {teacher_data.get('full_name', '')}",
                   font=FONTS["title"], fg=COLORS["primary"]).pack(pady=20)

          # إحصائيات عامة
          stats_frame = tk.Frame(scrollable_frame, bd=1, relief="solid", padx=10, pady=10)
          stats_frame.pack(fill="x", pady=10)

          tk.Label(stats_frame, text="الإحصائيات العامة",
                   font=FONTS["subtitle"], fg=COLORS["dark"]).pack(anchor="w")

          total_students = len(students)
          total_recitations = sum(len(s.get("recitations", [])) for s in students)
          avg_recitations = round(total_recitations / max(1, total_students), 1)

          stats_text = f"""
         عدد الطلاب: {total_students}
         عدد التسميعات: {total_recitations}
         متوسط التسميع لكل طالب: {avg_recitations}
         الطلاب الممتازون: {stats['ممتاز']}
         الطلاب الجيدون جدًا: {stats['جيد جدًا']}
         الطلاب الجيدون: {stats['جيد']}
         الطلاب المقبولون: {stats['مقبول']}
         """
          tk.Label(stats_frame, text=stats_text,
                   font=FONTS["normal"], justify="right").pack(anchor="e")

          # تفاصيل كل طالب
          for student in students:
               student_frame = tk.Frame(scrollable_frame, bd=1, relief="solid", padx=10, pady=10)
               student_frame.pack(fill="x", pady=5)

               tk.Label(student_frame, text=f"الطالب: {student['name']}",
                        font=FONTS["subtitle"], fg=COLORS["secondary"]).pack(anchor="w")

               student_stats = f"""
             المستوى: {student.get('level', 'غير محدد')}
             السورة الحالية: {student.get('current_surah', 'غير محدد')}
             الأداء: {student.get('performance', 'غير محدد')}
             عدد التسميعات: {len(student.get('recitations', []))}
             آخر تسميع: {student.get('recitations', [{}])[-1].get('date', 'لا يوجد')}
             """
               tk.Label(student_frame, text=student_stats,
                        font=FONTS["small"], justify="right").pack(anchor="e")

     # أزرار التحكم
     tk.Button(control_frame, text="طباعة التقرير", font=FONTS["small"], bg=COLORS["primary"],
               fg="white", command=print_report).pack(side="right", padx=10)
     tk.Button(control_frame, text="تصدير التقرير", font=FONTS["small"], bg=COLORS["success"],
               fg="white", command=export_report).pack(side="right", padx=10)
     tk.Button(control_frame, text="تقرير مفصل", font=FONTS["small"], bg=COLORS["info"],
               fg="white", command=generate_detailed_report).pack(side="right", padx=10)
     tk.Button(control_frame, text="تحديث البيانات", font=FONTS["small"], bg=COLORS["secondary"],
               fg="white", command=refresh).pack(side="left", padx=10)

     # تبويب تقارير الحضور (إضافة جديدة)
     attendance_frame = tk.Frame(report_nb)
     report_nb.add(attendance_frame, text="✅ تقارير الحضور")

     # إحصائيات الحضور
     att_stats_frame = tk.Frame(attendance_frame, bg="white", bd=1, relief="solid")
     att_stats_frame.pack(fill="x", padx=20, pady=20)

     tk.Label(att_stats_frame, text="إحصائيات الحضور",
              font=FONTS["subtitle"], bg="white").pack(pady=10)

     # حساب نسب الحضور
     attendance_records = []
     for student in students:
          attendance_records.extend(student.get("attendance", []))

     if attendance_records:
          present = sum(1 for a in attendance_records if a.get("status") == "حاضر")
          absent = sum(1 for a in attendance_records if a.get("status") == "غائب")
          total = len(attendance_records)
          present_percentage = round((present / total) * 100) if total > 0 else 0
          absent_percentage = round((absent / total) * 100) if total > 0 else 0

          # رسم بياني دائري مبسط
          canvas = tk.Canvas(att_stats_frame, width=300, height=200, bg="white")
          canvas.pack(pady=10)

          # رسم قطاع الحضور
          start_angle = 0
          extent = 360 * present_percentage / 100
          canvas.create_arc(50, 50, 250, 150, start=start_angle, extent=extent,
                            fill=COLORS["success"], outline="")

          # رسم قطاع الغياب
          start_angle += extent
          extent = 360 - extent
          canvas.create_arc(50, 50, 250, 150, start=start_angle, extent=extent,
                            fill=COLORS["danger"], outline="")

          # إضافة وسيلة إيضاح
          tk.Label(att_stats_frame, text=f"حاضر: {present_percentage}%",
                   font=FONTS["small"], fg=COLORS["success"]).pack()
          tk.Label(att_stats_frame, text=f"غائب: {absent_percentage}%",
                   font=FONTS["small"], fg=COLORS["danger"]).pack()
     else:
          tk.Label(att_stats_frame, text="لا توجد بيانات حضور متاحة",
                   font=FONTS["small"]).pack(pady=20)

     # تحديث البيانات عند فتح التبويب
     def on_tab_changed(event):
          selected_tab = report_nb.index(report_nb.select())
          if selected_tab == 2:  # تبويب الحضور
               refresh()

     report_nb.bind("<<NotebookTabChanged>>", on_tab_changed)

# ----- شاشة تسجيل الدخول مع تحسينات -----
def login_screen():
     global login_root, entry_user, entry_pass, role_var
     login_root = tk.Tk()
     login_root.title(f"{SYSTEM_NAME} | تسجيل الدخول")
     login_root.geometry("500x650")
     login_root.configure(bg="#f5f5f5")
     login_root.resizable(False, False)
     center_window(login_root, 600, 800)

     # تصميم شاشة الدخول
     header = tk.Frame(login_root, bg=COLORS["primary"], height=150)
     header.pack(fill="x")
     tk.Label(header, text=SYSTEM_NAME, font=("Cairo", 24, "bold"), bg=COLORS["primary"], fg="white").pack(pady=40)

     # إطار تسجيل الدخول
     login_frame = tk.Frame(login_root, bg="white", bd=1, relief="solid", padx=30, pady=30)
     login_frame.pack(fill="both", expand=True, padx=40, pady=40)
     tk.Label(login_frame, text="تسجيل الدخول", font=FONTS["title"], bg="white", fg=COLORS["primary"]).pack(pady=20)
     # حقل اسم المستخدم
     tk.Label(login_frame, text=":اسم المستخدم", font=FONTS["normal"], bg="white", anchor="e").pack(fill="x", pady=5)
     entry_user = ttk.Entry(login_frame, font=FONTS["normal"])
     entry_user.pack(fill="x", pady=5, ipady=5)
     # حقل كلمة المرور
     tk.Label(login_frame, text=":كلمة المرور", font=FONTS["normal"], bg="white", anchor="e").pack(fill="x", pady=5)
     entry_pass = ttk.Entry(login_frame, font=FONTS["normal"], show="*")
     entry_pass.pack(fill="x", pady=5, ipady=5)

     # حقل الدور المعدل
     tk.Label(
          login_frame,
          text=":الدور",
          font=("Cairo", 15),  # حجم الخط 14
          bg="white",
          anchor="e"
     ).pack(fill="x", pady=5)  # زيادة المسافة الرأسية إلى 8
     role_var = tk.StringVar(value="إدارة النظام")
     roles = ["إدارة النظام", "إدارة المركز", "هيئة التدريس"]
     role_combo = ttk.Combobox(
          login_frame,
          textvariable=role_var,
          values=roles,
          font=("Cairo", 15),  # حجم الخط 14
          state="readonly",
          height=10,  # عدد العناصر الظاهرة في القائمة المنسدلة
     )

     # تحسين مظهر القائمة المنسدلة
     style = ttk.Style()
     style.configure('TCombobox', padding=3)  # زيادة المساحة الداخلية
     role_combo.pack(
          fill="x",
          pady=5,  # زيادة ا+لمسافة الرأسية
          ipady=5  # زيادة الارتفاع الداخلي
     )

     # زر الدخول
     btn_login = tk.Button(login_frame, text="دخول", font=FONTS["subtitle"], bg=COLORS["primary"], fg="white",
                           relief="flat", command=check_login)
     btn_login.pack(fill="x", pady=20, ipady=10)

     # روابط إضافية
     links_frame = tk.Frame(login_frame, bg="white")
     links_frame.pack(fill="x")
     tk.Label(links_frame, text="نسيت كلمة المرور؟", font=FONTS["small"], fg=COLORS["secondary"], bg="white",
              cursor="hand2").pack(side="right", padx=5)
     tk.Label(links_frame, text="|", font=FONTS["small"], fg="#ccc", bg="white").pack(side="right", padx=5)
     tk.Label(links_frame, text="تغيير كلمة المرور", font=FONTS["small"], fg=COLORS["secondary"], bg="white",
              cursor="hand2").pack(side="right", padx=5)

     # تذييل الصفحة
     footer = tk.Frame(login_root, bg="#f5f5f5", height=50)
     footer.pack(fill="x", side="bottom")
     tk.Label(footer, text=f"الإصدار {VERSION} | {DEVELOPER} © 2025", font=FONTS["small"], bg="#f5f5f5").pack(pady=10)
     login_root.mainloop()

def check_login():
     username = entry_user.get().strip()
     password = entry_pass.get().strip()
     role = role_var.get().strip()

     user = next((u for u in USERS if u["username"] == username and u["password"] == password and u["role"] == role), None)

     if user:
          user["last_login"] = f"{get_today_date()} {get_current_time()}"
          save_data()
          login_root.destroy()

          if role == "إدارة النظام":
               open_main_dashboard_admin(username)
          elif role == "إدارة المركز":
               center_dashboard(username)
          elif role == "هيئة التدريس":
               teacher_data = get_teacher_data(username)
               if teacher_data:
                    teacher_dashboard(username)
               else:
                    messagebox.showerror("خطأ", "لا يوجد بيانات لهذا المعلم!")
                    login_screen()
     else:
          messagebox.showerror("خطأ", "بيانات الدخول غير صحيحة أو الدور غير مطابق!")

def show_help():
     help_text = """
    دليل استخدام نظام نور البيان

    1. إدارة النظام:
    - يمكنه إدارة جميع المراكز والمستخدمين
    - لديه صلاحية عرض جميع التقارير والإحصائيات
    - يمكنه إضافة مديري مراكز جدد

    2. إدارة المركز:
    - يمكنه إدارة المعلمين والطلاب في مركزه
    - لديه صلاحية عرض تقارير المركز
    - يمكنه إضافة معلمين جدد وتوزيع الطلاب عليهم

    3. المعلم:
    - يمكنه إدارة طلابه وتسجيل التسميعات
    - لديه صلاحية تسجيل الحضور والغياب
    - يمكنه إضافة طلاب جدد وتحديث بياناتهم

    للاستفسارات: {}
    """.format(CONTACT_EMAIL)
     messagebox.showinfo("مساعدة",help_text)

# تشغيل البرنامَج
if __name__ == "__main__":
     login_screen()
